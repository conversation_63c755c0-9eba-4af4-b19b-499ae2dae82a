{"name": "api-gateway", "version": "1.0.0", "description": "AnimeVerse API Gateway - Route requests, load balancing, authentication", "main": "app.js", "type": "module", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "cross-env NODE_ENV=test jest --runInBand --detectOpenHandles", "test:watch": "npm test -- --watch", "test:coverage": "npm test -- --coverage"}, "keywords": ["api-gateway", "microservices", "routing", "load-balancing"], "author": "AnimeVerse Team", "license": "MIT", "dependencies": {"express": "^5.1.0", "cors": "^2.8.5", "helmet": "^8.1.0", "morgan": "^1.10.0", "dotenv": "^17.2.0", "express-rate-limit": "^7.1.5", "winston": "^3.17.0", "axios": "^1.10.0", "http-proxy-middleware": "^3.0.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.1.10", "jest": "^30.0.4", "supertest": "^7.1.3", "cross-env": "^7.0.3"}}