import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';
import { createProxyMiddleware } from 'http-proxy-middleware';
import jwt from 'jsonwebtoken';

// Import utilities
import logger from './utils/logger.js';
import { errorHandler } from './middleware/errorHandler.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Higher limit for gateway
  message: { error: 'Too many requests from this IP' }
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:5173",
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined', {
  stream: { write: message => logger.info(message.trim()) }
}));

// Service URLs
const services = {
  user: process.env.USER_SERVICE_URL || 'http://localhost:3001',
  social: process.env.SOCIAL_SERVICE_URL || 'http://localhost:3002',
  event: process.env.EVENT_SERVICE_URL || 'http://localhost:3003',
  marketplace: process.env.MARKETPLACE_SERVICE_URL || 'http://localhost:3004',
  chat: process.env.CHAT_SERVICE_URL || 'http://localhost:3005',
  anime: process.env.ANIME_SERVICE_URL || 'http://localhost:3006'
};

// Authentication middleware for protected routes
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({
        error: 'Access token required',
        message: 'Please provide a valid authentication token'
      });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'animeverse_secret_key');
    req.user = decoded;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'Please provide a valid authentication token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        message: 'Your session has expired. Please log in again'
      });
    }
    
    return res.status(500).json({
      error: 'Authentication failed',
      message: 'Internal server error during authentication'
    });
  }
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    service: 'API Gateway',
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    services: services
  });
});

// Service health checks
app.get('/health/services', async (req, res) => {
  const healthChecks = {};
  
  for (const [name, url] of Object.entries(services)) {
    try {
      const response = await fetch(`${url}/health`, { 
        method: 'GET',
        timeout: 5000 
      });
      healthChecks[name] = {
        status: response.ok ? 'healthy' : 'unhealthy',
        url: url
      };
    } catch (error) {
      healthChecks[name] = {
        status: 'unreachable',
        url: url,
        error: error.message
      };
    }
  }
  
  res.json({
    gateway: 'healthy',
    services: healthChecks,
    timestamp: new Date().toISOString()
  });
});

// Proxy configurations
const proxyOptions = {
  changeOrigin: true,
  timeout: 30000,
  onError: (err, req, res) => {
    logger.error(`Proxy error: ${err.message}`);
    res.status(503).json({
      error: 'Service unavailable',
      message: 'The requested service is currently unavailable'
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    // Add user info to headers for downstream services
    if (req.user) {
      proxyReq.setHeader('X-User-ID', req.user.userId);
      proxyReq.setHeader('X-User-Role', req.user.role || 'user');
    }
  }
};

// Route to User Service (authentication routes are public)
app.use('/api/auth', createProxyMiddleware({
  target: services.user,
  ...proxyOptions,
  pathRewrite: { '^/api/auth': '/api/auth' }
}));

// Protected routes - require authentication
app.use('/api/users', authenticateToken, createProxyMiddleware({
  target: services.user,
  ...proxyOptions,
  pathRewrite: { '^/api/users': '/api/users' }
}));

app.use('/api/posts', authenticateToken, createProxyMiddleware({
  target: services.social,
  ...proxyOptions,
  pathRewrite: { '^/api/posts': '/api/posts' }
}));

app.use('/api/feed', authenticateToken, createProxyMiddleware({
  target: services.social,
  ...proxyOptions,
  pathRewrite: { '^/api/feed': '/api/feed' }
}));

app.use('/api/events', authenticateToken, createProxyMiddleware({
  target: services.event,
  ...proxyOptions,
  pathRewrite: { '^/api/events': '/api/events' }
}));

app.use('/api/marketplace', authenticateToken, createProxyMiddleware({
  target: services.marketplace,
  ...proxyOptions,
  pathRewrite: { '^/api/marketplace': '/api/marketplace' }
}));

app.use('/api/chat', authenticateToken, createProxyMiddleware({
  target: services.chat,
  ...proxyOptions,
  pathRewrite: { '^/api/chat': '/api/chat' }
}));

app.use('/api/notifications', authenticateToken, createProxyMiddleware({
  target: services.chat,
  ...proxyOptions,
  pathRewrite: { '^/api/notifications': '/api/notifications' }
}));

app.use('/api/anime', authenticateToken, createProxyMiddleware({
  target: services.anime,
  ...proxyOptions,
  pathRewrite: { '^/api/anime': '/api/anime' }
}));

// Clan routes (part of user service)
app.use('/api/clans', authenticateToken, createProxyMiddleware({
  target: services.user,
  ...proxyOptions,
  pathRewrite: { '^/api/clans': '/api/clans' }
}));

// Settings routes (part of user service)
app.use('/api/settings', authenticateToken, createProxyMiddleware({
  target: services.user,
  ...proxyOptions,
  pathRewrite: { '^/api/settings': '/api/settings' }
}));

// Admin routes (part of user service)
app.use('/api/admin', authenticateToken, createProxyMiddleware({
  target: services.user,
  ...proxyOptions,
  pathRewrite: { '^/api/admin': '/api/admin' }
}));

// WebSocket proxy for chat service
app.use('/socket.io', createProxyMiddleware({
  target: services.chat,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  ...proxyOptions
}));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    availableServices: Object.keys(services)
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🌐 API Gateway running on port ${PORT}`);
  logger.info(`🔗 Routing to services:`, services);
  logger.info(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

export default app;
