# AnimeVerse Backend Server

A comprehensive Node.js backend server for the AnimeVerse social platform, built with Express.js, MongoDB, and Socket.IO.

## 🚀 Features

### Core Features
- **User Authentication & Authorization** - JWT-based auth with role-based access control
- **Real-time Communication** - Socket.IO for chat, notifications, and live updates
- **Social Features** - Posts, comments, likes, follows, and user interactions
- **Clan System** - Anime-themed clans with rankings, activities, and leaderboards
- **Event Management** - Create and manage anime conventions, watch parties, and meetups
- **Marketplace** - Buy/sell anime merchandise with trading capabilities
- **Notification System** - Real-time and persistent notifications
- **Admin Panel** - Content moderation, user management, and analytics

### Technical Features
- **MongoDB Integration** - Comprehensive data models with relationships
- **Security** - Helmet, CORS, rate limiting, input validation
- **Error Handling** - Centralized error management with proper HTTP responses
- **File Upload** - Support for images and media files
- **API Documentation** - RESTful API design with consistent responses

## 📁 Project Structure

```
server/
├── src/
│   ├── config/
│   │   └── database.js          # MongoDB connection
│   ├── middleware/
│   │   ├── auth.js              # Authentication middleware
│   │   └── errorHandler.js      # Error handling middleware
│   ├── models/
│   │   ├── User.js              # User data model
│   │   ├── Post.js              # Social posts model
│   │   ├── Clan.js              # Clan system model
│   │   ├── Event.js             # Events model
│   │   ├── Marketplace.js       # Marketplace items model
│   │   ├── Chat.js              # Chat messages model
│   │   └── Notification.js      # Notifications model
│   ├── routes/
│   │   ├── auth.js              # Authentication routes
│   │   ├── users.js             # User management routes
│   │   ├── posts.js             # Social posts routes
│   │   ├── clans.js             # Clan system routes
│   │   ├── events.js            # Event management routes
│   │   ├── marketplace.js       # Marketplace routes
│   │   ├── chat.js              # Chat system routes
│   │   ├── notifications.js     # Notification routes
│   │   ├── settings.js          # User settings routes
│   │   ├── anime.js             # Anime data routes
│   │   └── admin.js             # Admin panel routes
│   ├── scripts/
│   │   └── initDatabase.js      # Database initialization
│   ├── utils/
│   │   ├── socket.js            # Socket.IO handlers
│   │   └── helpers.js           # Utility functions
│   └── server.js                # Main server file
├── package.json
├── .env.example
└── README.md
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (v5.0 or higher)
- npm or yarn

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd server
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   NODE_ENV=development
   PORT=5000
   CLIENT_URL=http://localhost:5173
   MONGODB_URI=mongodb://localhost:27017/animeverse
   JWT_SECRET=your_super_secret_jwt_key_here
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Initialize Database**
   ```bash
   npm run init-db
   ```
   This creates default clans and sample data.

6. **Start the server**
   ```bash
   # Development mode with auto-reload
   npm run dev
   
   # Production mode
   npm start
   ```

The server will start on `http://localhost:5000`

## 📊 Database Models

### User Model
- Authentication & profile information
- Clan membership & rankings
- Anime watching statistics
- Social connections (followers/following)
- Gamification (levels, achievements, points)
- Privacy & notification settings

### Post Model
- Social media posts with rich content
- Support for text, images, reviews, discussions
- Engagement tracking (likes, comments, shares)
- Anime references and tagging
- Visibility controls and moderation

### Clan Model
- Five anime-themed clans (Naruto, One Piece, etc.)
- Hierarchical ranking systems
- Activities, challenges, and tournaments
- Leaderboards and statistics
- Recent activity tracking

### Event Model
- Conventions, meetups, watch parties
- RSVP system with capacity management
- Location support (online/physical)
- Anime-specific events and tournaments
- Notification and reminder system

### Marketplace Model
- Anime merchandise trading
- Comprehensive item details and images
- Pricing, condition, and shipping info
- Wishlist and trading capabilities
- User ratings and reviews

## 🔐 Authentication & Authorization

### JWT Authentication
- Secure token-based authentication
- Refresh token support (planned)
- Role-based access control (user, moderator, admin)
- Session management and security

### User Roles
- **User**: Standard user permissions
- **Moderator**: Content moderation capabilities
- **Admin**: Full system access and management

### Protected Routes
Most API endpoints require authentication. Use the `Authorization` header:
```
Authorization: Bearer <your-jwt-token>
```

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/clans` - Get available clans

### Users
- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update profile
- `GET /api/users/:identifier` - Get user by ID/username
- `POST /api/users/:userId/follow` - Follow/unfollow user
- `GET /api/users/search/:query` - Search users

### Posts
- `GET /api/posts/feed` - Get posts feed
- `POST /api/posts` - Create new post
- `GET /api/posts/:postId` - Get single post
- `POST /api/posts/:postId/like` - Like/unlike post
- `POST /api/posts/:postId/comments` - Add comment

### Clans
- `GET /api/clans` - Get all clans
- `GET /api/clans/:clanName` - Get clan details
- `GET /api/clans/:clanName/members` - Get clan members
- `GET /api/clans/:clanName/activities` - Get clan activities

### Events
- `GET /api/events` - Get events
- `POST /api/events` - Create event
- `GET /api/events/:eventId` - Get event details
- `POST /api/events/:eventId/rsvp` - RSVP to event

### Marketplace
- `GET /api/marketplace` - Get marketplace items
- `POST /api/marketplace` - List new item
- `GET /api/marketplace/:itemId` - Get item details
- `POST /api/marketplace/:itemId/wishlist` - Toggle wishlist

## 🔄 Real-time Features (Socket.IO)

### Chat System
- Direct messages between users
- Group chats and clan channels
- Real-time message delivery
- Typing indicators and read receipts
- Voice/video call support (planned)

### Notifications
- Real-time notification delivery
- Push notifications for offline users
- Notification categories and preferences
- Mark as read functionality

### Live Updates
- Real-time post likes and comments
- User online/offline status
- Event updates and reminders
- Marketplace item changes

## 🛡️ Security Features

- **Helmet.js** - Security headers
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - API request throttling
- **Input Validation** - Request data validation
- **Password Hashing** - bcrypt encryption
- **JWT Security** - Token-based authentication
- **Error Handling** - Secure error responses

## 📈 Admin Features

### Analytics Dashboard
- User engagement metrics
- Content statistics
- Clan distribution analysis
- System performance monitoring

### Content Moderation
- Report management system
- Content approval/removal
- User account management
- Automated moderation tools

### System Management
- Database backup utilities
- Maintenance mode toggle
- System announcements
- Performance monitoring

## 🧪 Development

### Available Scripts
- `npm run dev` - Start development server with auto-reload
- `npm start` - Start production server
- `npm run init-db` - Initialize database with sample data
- `npm test` - Run tests (to be implemented)

### Code Style
- ES6+ JavaScript with modules
- Async/await for asynchronous operations
- Consistent error handling patterns
- RESTful API design principles

## 🚀 Deployment

### Environment Variables
Ensure all required environment variables are set in production:
- `NODE_ENV=production`
- `MONGODB_URI` - Production MongoDB connection
- `JWT_SECRET` - Strong JWT secret key
- `CLIENT_URL` - Frontend application URL

### Production Considerations
- Use process managers (PM2, Docker)
- Set up MongoDB replica sets
- Configure reverse proxy (Nginx)
- Enable SSL/TLS certificates
- Set up monitoring and logging
- Configure backup strategies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**AnimeVerse Backend** - Powering the ultimate anime social experience! 🌸
