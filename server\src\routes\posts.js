import express from 'express';
import { body, validationResult } from 'express-validator';
import Post from '../models/Post.js';
import User from '../models/User.js';
import Notification from '../models/Notification.js';
import { requireOwnershipOrAdmin, requireModerator } from '../middleware/auth.js';
import { catchAsync, AppError, notFoundError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get posts feed
router.get('/feed', catchAsync(async (req, res) => {
  const { page = 1, limit = 20, type, clan } = req.query;
  
  const query = {
    isActive: true,
    visibility: { $in: ['public', 'clan'] }
  };
  
  if (type) query.type = type;
  if (clan) {
    // Get posts from users in specific clan
    const clanUsers = await User.find({ clan, isActive: true }).select('_id');
    query.author = { $in: clanUsers.map(u => u._id) };
  }
  
  const posts = await Post.find(query)
    .populate('author', 'username avatar clan clanRank level')
    .populate('comments.user', 'username avatar clan')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));
  
  // Add engagement data for current user
  const postsWithEngagement = posts.map(post => {
    const postObj = post.toObject();
    postObj.isLikedByUser = post.isLikedBy(req.user._id);
    postObj.likeCount = post.likes.length;
    postObj.commentCount = post.comments.length;
    return postObj;
  });
  
  const total = await Post.countDocuments(query);
  
  res.json({
    success: true,
    posts: postsWithEngagement,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Get trending posts
router.get('/trending', catchAsync(async (req, res) => {
  const { limit = 10 } = req.query;
  
  const trendingPosts = await Post.getTrending(parseInt(limit));
  
  // Populate author information
  await Post.populate(trendingPosts, {
    path: 'author',
    select: 'username avatar clan clanRank level'
  });
  
  res.json({
    success: true,
    posts: trendingPosts
  });
}));

// Create new post
router.post('/', [
  body('content')
    .isLength({ min: 1, max: 2000 })
    .withMessage('Content must be between 1 and 2000 characters'),
  
  body('type')
    .optional()
    .isIn(['text', 'image', 'review', 'discussion', 'fanart', 'news'])
    .withMessage('Invalid post type'),
  
  body('visibility')
    .optional()
    .isIn(['public', 'friends', 'clan', 'private'])
    .withMessage('Invalid visibility setting')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const {
    content,
    type = 'text',
    visibility = 'public',
    images = [],
    animeReference,
    review,
    tags = []
  } = req.body;
  
  const post = new Post({
    author: req.user._id,
    content,
    type,
    visibility,
    images,
    animeReference,
    review,
    tags
  });
  
  await post.save();
  await post.populate('author', 'username avatar clan clanRank level');
  
  // Award experience points
  const user = await User.findById(req.user._id);
  user.experience += 10; // Base points for creating a post
  if (user.experience >= user.level * 100) {
    user.level += 1;
    user.experience = 0;
    
    // Create level up notification
    await Notification.createNotification({
      recipient: user._id,
      type: 'level_up',
      title: 'Level Up!',
      message: `Congratulations! You've reached level ${user.level}`,
      actionUrl: '/profile/me'
    });
  }
  await user.save();
  
  res.status(201).json({
    success: true,
    message: 'Post created successfully',
    post: {
      ...post.toObject(),
      isLikedByUser: false,
      likeCount: 0,
      commentCount: 0
    }
  });
}));

// Get single post
router.get('/:postId', catchAsync(async (req, res) => {
  const { postId } = req.params;
  
  const post = await Post.findById(postId)
    .populate('author', 'username avatar clan clanRank level')
    .populate('comments.user', 'username avatar clan')
    .populate('comments.replies.user', 'username avatar clan');
  
  if (!post || !post.isActive) {
    throw notFoundError('Post');
  }
  
  // Check visibility permissions
  if (post.visibility === 'private' && post.author._id.toString() !== req.user._id.toString()) {
    throw new AppError('This post is private', 403, 'PRIVATE_POST');
  }
  
  if (post.visibility === 'friends') {
    const author = await User.findById(post.author._id);
    const isFriend = author.friends.includes(req.user._id);
    
    if (!isFriend && post.author._id.toString() !== req.user._id.toString()) {
      throw new AppError('This post is only visible to friends', 403, 'FRIENDS_ONLY');
    }
  }
  
  // Increment view count
  await post.incrementViews(req.user._id);
  
  const postObj = post.toObject();
  postObj.isLikedByUser = post.isLikedBy(req.user._id);
  postObj.likeCount = post.likes.length;
  postObj.commentCount = post.comments.length;
  
  res.json({
    success: true,
    post: postObj
  });
}));

// Update post
router.put('/:postId', [
  body('content')
    .optional()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Content must be between 1 and 2000 characters'),
  
  body('visibility')
    .optional()
    .isIn(['public', 'friends', 'clan', 'private'])
    .withMessage('Invalid visibility setting')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { postId } = req.params;
  const { content, visibility, tags } = req.body;
  
  const post = await Post.findById(postId);
  if (!post || !post.isActive) {
    throw notFoundError('Post');
  }
  
  // Check ownership
  if (post.author.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    throw new AppError('You can only edit your own posts', 403, 'NOT_OWNER');
  }
  
  const updateData = {};
  if (content) updateData.content = content;
  if (visibility) updateData.visibility = visibility;
  if (tags) updateData.tags = tags;
  
  const updatedPost = await Post.findByIdAndUpdate(
    postId,
    updateData,
    { new: true, runValidators: true }
  ).populate('author', 'username avatar clan clanRank level');
  
  res.json({
    success: true,
    message: 'Post updated successfully',
    post: updatedPost
  });
}));

// Delete post
router.delete('/:postId', catchAsync(async (req, res) => {
  const { postId } = req.params;
  
  const post = await Post.findById(postId);
  if (!post || !post.isActive) {
    throw notFoundError('Post');
  }
  
  // Check ownership or admin
  if (post.author.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    throw new AppError('You can only delete your own posts', 403, 'NOT_OWNER');
  }
  
  // Soft delete
  post.isActive = false;
  await post.save();
  
  res.json({
    success: true,
    message: 'Post deleted successfully'
  });
}));

// Like/Unlike post
router.post('/:postId/like', catchAsync(async (req, res) => {
  const { postId } = req.params;
  
  const post = await Post.findById(postId);
  if (!post || !post.isActive) {
    throw notFoundError('Post');
  }
  
  const isLiked = post.isLikedBy(req.user._id);
  
  if (isLiked) {
    await post.removeLike(req.user._id);
  } else {
    await post.addLike(req.user._id);
    
    // Create notification for post author (if not self-like)
    if (post.author.toString() !== req.user._id.toString()) {
      await Notification.createNotification({
        recipient: post.author,
        sender: req.user._id,
        type: 'like',
        title: 'New Like',
        message: `${req.user.username} liked your post`,
        actionUrl: `/posts/${postId}`,
        metadata: { postId }
      });
    }
  }
  
  res.json({
    success: true,
    message: isLiked ? 'Post unliked' : 'Post liked',
    isLiked: !isLiked,
    likeCount: post.likes.length + (isLiked ? -1 : 1)
  });
}));

// Add comment to post
router.post('/:postId/comments', [
  body('content')
    .isLength({ min: 1, max: 500 })
    .withMessage('Comment must be between 1 and 500 characters')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { postId } = req.params;
  const { content } = req.body;
  
  const post = await Post.findById(postId);
  if (!post || !post.isActive) {
    throw notFoundError('Post');
  }
  
  await post.addComment(req.user._id, content);
  await post.populate('comments.user', 'username avatar clan');
  
  const newComment = post.comments[post.comments.length - 1];
  
  // Create notification for post author (if not self-comment)
  if (post.author.toString() !== req.user._id.toString()) {
    await Notification.createNotification({
      recipient: post.author,
      sender: req.user._id,
      type: 'comment',
      title: 'New Comment',
      message: `${req.user.username} commented on your post`,
      actionUrl: `/posts/${postId}`,
      metadata: { postId }
    });
  }
  
  res.status(201).json({
    success: true,
    message: 'Comment added successfully',
    comment: newComment
  });
}));

// Get post comments
router.get('/:postId/comments', catchAsync(async (req, res) => {
  const { postId } = req.params;
  const { page = 1, limit = 20 } = req.query;
  
  const post = await Post.findById(postId)
    .populate('comments.user', 'username avatar clan')
    .populate('comments.replies.user', 'username avatar clan');
  
  if (!post || !post.isActive) {
    throw notFoundError('Post');
  }
  
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const comments = post.comments.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    comments,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: post.comments.length
    }
  });
}));

// Report post
router.post('/:postId/report', [
  body('reason')
    .isIn(['spam', 'harassment', 'inappropriate', 'copyright', 'other'])
    .withMessage('Invalid report reason'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { postId } = req.params;
  const { reason, description } = req.body;
  
  const post = await Post.findById(postId);
  if (!post || !post.isActive) {
    throw notFoundError('Post');
  }
  
  // Check if user already reported this post
  const existingReport = post.reports.find(report => 
    report.reporter.toString() === req.user._id.toString()
  );
  
  if (existingReport) {
    throw new AppError('You have already reported this post', 400, 'ALREADY_REPORTED');
  }
  
  post.reports.push({
    reporter: req.user._id,
    reason,
    description
  });
  
  post.isReported = true;
  await post.save();
  
  res.json({
    success: true,
    message: 'Post reported successfully'
  });
}));

// Get user's posts
router.get('/user/:userId', catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20, type } = req.query;
  
  const query = {
    author: userId,
    isActive: true
  };
  
  if (type) query.type = type;
  
  // Check if viewing own posts or public posts
  if (userId !== req.user._id.toString()) {
    query.visibility = 'public';
  }
  
  const posts = await Post.find(query)
    .populate('author', 'username avatar clan clanRank level')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));
  
  const total = await Post.countDocuments(query);
  
  res.json({
    success: true,
    posts,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Search posts
router.get('/search/:query', catchAsync(async (req, res) => {
  const { query } = req.params;
  const { page = 1, limit = 20, type, clan } = req.query;
  
  if (!query || query.length < 2) {
    throw new AppError('Search query must be at least 2 characters', 400);
  }
  
  const searchQuery = {
    $and: [
      { isActive: true },
      { visibility: 'public' },
      {
        $or: [
          { content: { $regex: query, $options: 'i' } },
          { tags: { $regex: query, $options: 'i' } }
        ]
      }
    ]
  };
  
  if (type) searchQuery.$and.push({ type });
  
  if (clan) {
    const clanUsers = await User.find({ clan, isActive: true }).select('_id');
    searchQuery.$and.push({ author: { $in: clanUsers.map(u => u._id) } });
  }
  
  const posts = await Post.find(searchQuery)
    .populate('author', 'username avatar clan clanRank level')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));
  
  const total = await Post.countDocuments(searchQuery);
  
  res.json({
    success: true,
    posts,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

export default router;
