import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const connectDB = async (serviceName = 'service') => {
  try {
    // Use service-specific database or shared database
    const dbName = process.env.DB_NAME || 'animeverse';
    const mongoUri = process.env.MONGODB_URI || `mongodb://localhost:27017/${dbName}`;
    
    const conn = await mongoose.connect(mongoUri, {
      // Remove deprecated options
    });

    console.log(`🍃 ${serviceName} MongoDB Connected: ${conn.connection.host}`);
    console.log(`📊 Database: ${conn.connection.name}`);

    // Handle connection events
    mongoose.connection.on('error', (err) => {
      console.error(`${serviceName} MongoDB connection error:`, err);
    });

    mongoose.connection.on('disconnected', () => {
      console.log(`${serviceName} MongoDB disconnected`);
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      console.log(`${serviceName} MongoDB connection closed through app termination`);
      process.exit(0);
    });

  } catch (error) {
    console.error(`${serviceName} Error connecting to MongoDB:`, error.message);
    process.exit(1);
  }
};

export default connectDB;
