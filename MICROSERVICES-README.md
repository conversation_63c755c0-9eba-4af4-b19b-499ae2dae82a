# 🌸 AnimeVerse Microservices Architecture

A comprehensive microservices-based anime social platform built with Node.js, MongoDB, and React.

## 🏗️ **Architecture Overview**

### **Microservices Structure:**
```
AnimeVerse/
├── services/
│   ├── API-Gateway/          # 🌐 Main entry point (Port 3000)
│   ├── Social-Service/       # 📱 Posts, feed, social features (Port 3002)
│   ├── Event-Service/        # 🎪 Events, RSVP, calendar (Port 3003)
│   ├── Marketplace-Service/  # 🛒 Trading, items, wishlist (Port 3004)
│   ├── Chat-Service/         # 💬 Real-time chat, notifications (Port 3005)
│   └── Anime-Service/        # 📺 Anime data, recommendations (Port 3006)
├── User-Service/             # 👥 Authentication, users, clans (Port 3001)
├── client/                   # ⚛️ React frontend (Port 5173)
├── shared/                   # 🔧 Shared utilities and configs
└── docker-compose.yml        # 🐳 Container orchestration
```

### **Service Responsibilities:**

#### **🌐 API Gateway (Port 3000)**
- **Purpose**: Single entry point for all client requests
- **Features**: 
  - Request routing to appropriate services
  - Authentication middleware
  - Rate limiting and security
  - Load balancing
  - Service health monitoring
- **Routes**: All `/api/*` routes

#### **👥 User Service (Port 3001)**
- **Purpose**: User management and authentication
- **Features**:
  - User registration/login/logout
  - Profile management
  - Clan system (5 anime clans)
  - User settings and preferences
  - Admin panel and analytics
- **Routes**: `/api/auth/*`, `/api/users/*`, `/api/clans/*`, `/api/settings/*`, `/api/admin/*`

#### **📱 Social Service (Port 3002)**
- **Purpose**: Social media features
- **Features**:
  - Posts (text, images, reviews, discussions)
  - Comments and replies
  - Like/unlike system
  - Feed algorithms
  - Trending content
- **Routes**: `/api/posts/*`, `/api/feed/*`

#### **🎪 Event Service (Port 3003)**
- **Purpose**: Event management
- **Features**:
  - Create/manage events (conventions, watch parties, meetups)
  - RSVP system with capacity management
  - Event calendar and reminders
  - Online and physical events
- **Routes**: `/api/events/*`

#### **🛒 Marketplace Service (Port 3004)**
- **Purpose**: Anime merchandise trading
- **Features**:
  - Item listings with images and details
  - Wishlist functionality
  - Trading system
  - Search and filtering
  - User ratings and reviews
- **Routes**: `/api/marketplace/*`

#### **💬 Chat Service (Port 3005)**
- **Purpose**: Real-time communication
- **Features**:
  - Direct and group messaging
  - Real-time notifications
  - Typing indicators
  - Online status tracking
  - WebSocket support
- **Routes**: `/api/chat/*`, `/api/notifications/*`, `/socket.io/*`

#### **📺 Anime Service (Port 3006)**
- **Purpose**: Anime data and recommendations
- **Features**:
  - Anime database integration
  - Search and filtering
  - Recommendations engine
  - User anime tracking
  - External API integration (Jikan)
- **Routes**: `/api/anime/*`

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+
- MongoDB 5.0+
- Docker & Docker Compose (optional)

### **Option 1: Local Development**

1. **Install Dependencies**
   ```bash
   npm run install:all
   ```

2. **Set Up Environment**
   ```bash
   # Copy environment files for each service
   cp User-Service/.env.example User-Service/.env
   cp services/API-Gateway/.env.example services/API-Gateway/.env
   # ... repeat for other services
   ```

3. **Start MongoDB**
   ```bash
   # Using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:7.0
   
   # Or use your local MongoDB installation
   ```

4. **Initialize Database**
   ```bash
   npm run init:db
   ```

5. **Start All Services**
   ```bash
   npm run dev
   ```

### **Option 2: Docker Compose**

1. **Start Everything**
   ```bash
   docker-compose up -d
   ```

2. **View Logs**
   ```bash
   docker-compose logs -f
   ```

3. **Stop Everything**
   ```bash
   docker-compose down
   ```

## 🔗 **Service Communication**

### **API Gateway Routes:**
- **Frontend** → **API Gateway** (Port 3000)
- **API Gateway** → **Individual Services** (Internal routing)

### **Inter-Service Communication:**
- Services communicate via HTTP REST APIs
- Authentication tokens passed through headers
- Shared database for data consistency
- Event-driven architecture for real-time features

### **Service URLs:**
```
API Gateway:    http://localhost:3000
User Service:   http://localhost:3001
Social Service: http://localhost:3002
Event Service:  http://localhost:3003
Marketplace:    http://localhost:3004
Chat Service:   http://localhost:3005
Anime Service:  http://localhost:3006
Frontend:       http://localhost:5173
```

## 🛡️ **Security & Authentication**

### **JWT Authentication Flow:**
1. User logs in via API Gateway → User Service
2. User Service returns JWT token
3. Client includes token in all subsequent requests
4. API Gateway validates token before routing
5. User info passed to services via headers

### **Security Features:**
- Rate limiting per service
- Input validation and sanitization
- CORS configuration
- Helmet security headers
- Error handling without data leaks

## 📊 **Monitoring & Health Checks**

### **Health Endpoints:**
```bash
# Gateway health
curl http://localhost:3000/health

# All services health
curl http://localhost:3000/health/services

# Individual service health
curl http://localhost:3001/health  # User Service
curl http://localhost:3002/health  # Social Service
# ... etc
```

### **Logging:**
- Winston logging in all services
- Service-specific log files
- Centralized error tracking
- Request/response logging

## 🧪 **Testing**

### **Run All Tests:**
```bash
npm test
```

### **Run Individual Service Tests:**
```bash
npm run test:user
npm run test:social
npm run test:event
# ... etc
```

## 🔧 **Development**

### **Adding a New Service:**

1. **Create Service Directory:**
   ```bash
   mkdir services/New-Service
   cd services/New-Service
   ```

2. **Initialize Package:**
   ```bash
   npm init -y
   # Install dependencies
   npm install express mongoose cors helmet morgan dotenv
   ```

3. **Create Service Structure:**
   ```
   New-Service/
   ├── app.js
   ├── routes/
   ├── models/
   ├── middleware/
   └── utils/
   ```

4. **Update API Gateway:**
   - Add service URL to environment
   - Add proxy routes
   - Update health checks

5. **Update Docker Compose:**
   - Add service configuration
   - Set environment variables
   - Configure networking

### **Service Development Guidelines:**
- Each service should be independently deployable
- Use shared utilities from `/shared` directory
- Follow consistent error handling patterns
- Implement proper logging
- Include health check endpoints
- Write comprehensive tests

## 🐳 **Docker Configuration**

### **Individual Service Dockerfiles:**
Each service includes a Dockerfile for containerization.

### **Docker Compose Features:**
- Multi-service orchestration
- Shared networking
- Volume persistence
- Environment configuration
- Service dependencies

### **Production Deployment:**
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

## 📈 **Scaling & Performance**

### **Horizontal Scaling:**
- Each service can be scaled independently
- Load balancing via API Gateway
- Database connection pooling
- Redis for caching and sessions

### **Performance Optimizations:**
- Database indexing
- Query optimization
- Caching strategies
- Connection pooling
- Compression middleware

## 🔄 **CI/CD Pipeline**

### **Automated Testing:**
- Unit tests for each service
- Integration tests
- End-to-end testing
- Code coverage reports

### **Deployment Strategy:**
- Blue-green deployments
- Rolling updates
- Health check validation
- Rollback capabilities

## 🆘 **Troubleshooting**

### **Common Issues:**

1. **Service Not Starting:**
   ```bash
   # Check logs
   docker-compose logs service-name
   
   # Check health
   curl http://localhost:PORT/health
   ```

2. **Database Connection Issues:**
   ```bash
   # Check MongoDB status
   docker-compose logs mongodb
   
   # Verify connection string
   echo $MONGODB_URI
   ```

3. **Port Conflicts:**
   ```bash
   # Check port usage
   netstat -tulpn | grep :3000
   
   # Update port in docker-compose.yml
   ```

## 🤝 **Contributing**

1. Fork the repository
2. Create feature branch
3. Follow service-specific guidelines
4. Add tests for new features
5. Update documentation
6. Submit pull request

## 📄 **License**

MIT License - see LICENSE file for details.

---

**AnimeVerse Microservices** - Scalable, maintainable, and production-ready! 🌸
