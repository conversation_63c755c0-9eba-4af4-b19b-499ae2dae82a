import mongoose from 'mongoose';

const notificationSchema = new mongoose.Schema({
  // Recipient
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Sender (optional, for user-generated notifications)
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Notification Type
  type: {
    type: String,
    enum: [
      // Social notifications
      'like', 'comment', 'follow', 'mention', 'share',
      
      // Episode notifications
      'episode_release', 'episode_reminder', 'anime_update',
      
      // Event notifications
      'event_reminder', 'event_update', 'event_cancelled', 'event_invitation',
      
      // Marketplace notifications
      'price_drop', 'item_sold', 'item_purchased', 'trade_offer', 'wishlist_available',
      
      // Clan notifications
      'clan_promotion', 'clan_activity', 'clan_challenge', 'clan_achievement',
      
      // System notifications
      'system_update', 'maintenance', 'achievement_unlocked', 'level_up',
      
      // Chat notifications
      'message', 'call_missed', 'group_added'
    ],
    required: true
  },
  
  // Content
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  
  message: {
    type: String,
    required: true,
    maxlength: 500
  },
  
  // Visual elements
  icon: String, // Emoji or icon identifier
  
  color: {
    type: String,
    enum: ['red', 'blue', 'green', 'purple', 'orange', 'yellow', 'gray'],
    default: 'blue'
  },
  
  // Action URL (where to redirect when clicked)
  actionUrl: String,
  
  // Related data
  metadata: {
    // Post related
    postId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Post'
    },
    
    // Event related
    eventId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Event'
    },
    
    // Marketplace related
    itemId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'MarketplaceItem'
    },
    
    // Chat related
    chatId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Chat'
    },
    
    // Anime related
    animeId: String,
    animeTitle: String,
    episode: Number,
    
    // Clan related
    clan: String,
    oldRank: String,
    newRank: String,
    
    // Achievement related
    achievementId: String,
    achievementName: String,
    
    // Price related
    oldPrice: Number,
    newPrice: Number,
    discount: Number,
    
    // Generic data
    customData: mongoose.Schema.Types.Mixed
  },
  
  // Status
  isRead: {
    type: Boolean,
    default: false
  },
  
  readAt: Date,
  
  // Priority
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Delivery
  deliveryMethod: {
    email: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    push: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    inApp: {
      sent: { type: Boolean, default: true },
      sentAt: { type: Date, default: Date.now }
    }
  },
  
  // Scheduling
  scheduledFor: Date, // For future notifications
  
  // Expiry
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  },
  
  // Grouping (for similar notifications)
  groupKey: String, // e.g., "likes_post_123" to group multiple likes on same post
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ recipient: 1, isRead: 1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ scheduledFor: 1 });
notificationSchema.index({ groupKey: 1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index

// Compound indexes
notificationSchema.index({ recipient: 1, type: 1, createdAt: -1 });
notificationSchema.index({ recipient: 1, isRead: 1, createdAt: -1 });

// Virtual for time ago
notificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diffInMinutes = Math.floor((now - this.createdAt) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
});

// Method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  return this.save();
};

// Method to check if notification should be sent based on user settings
notificationSchema.methods.shouldSendToUser = async function() {
  const User = mongoose.model('User');
  const user = await User.findById(this.recipient);
  
  if (!user) return false;
  
  const settings = user.settings;
  
  // Check if user has notifications enabled for this type
  switch (this.type) {
    case 'like':
    case 'comment':
    case 'follow':
    case 'mention':
    case 'share':
      return settings.socialNotifications;
      
    case 'episode_release':
    case 'episode_reminder':
    case 'anime_update':
      return settings.episodeAlerts;
      
    case 'event_reminder':
    case 'event_update':
    case 'event_cancelled':
    case 'event_invitation':
      return settings.eventReminders;
      
    case 'price_drop':
    case 'item_sold':
    case 'item_purchased':
    case 'trade_offer':
    case 'wishlist_available':
      return settings.marketplaceAlerts;
      
    case 'clan_promotion':
    case 'clan_activity':
    case 'clan_challenge':
    case 'clan_achievement':
      return settings.clanNotifications;
      
    case 'message':
    case 'call_missed':
    case 'group_added':
      return true; // Always send chat notifications
      
    default:
      return true; // Default to sending system notifications
  }
};

// Static method to create notification
notificationSchema.statics.createNotification = async function(data) {
  const notification = new this(data);
  
  // Check if user wants this type of notification
  const shouldSend = await notification.shouldSendToUser();
  if (!shouldSend) {
    return null; // Don't create notification if user has it disabled
  }
  
  // Check for grouping
  if (data.groupKey) {
    const existingNotification = await this.findOne({
      recipient: data.recipient,
      groupKey: data.groupKey,
      isRead: false,
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Within last 24 hours
    });
    
    if (existingNotification) {
      // Update existing notification instead of creating new one
      existingNotification.message = data.message;
      existingNotification.createdAt = new Date();
      return existingNotification.save();
    }
  }
  
  return notification.save();
};

// Static method to get user notifications
notificationSchema.statics.getUserNotifications = function(userId, options = {}) {
  const {
    limit = 50,
    skip = 0,
    type = null,
    isRead = null
  } = options;
  
  const query = { recipient: userId };
  
  if (type) query.type = type;
  if (isRead !== null) query.isRead = isRead;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('sender', 'username avatar clan')
    .populate('metadata.postId', 'content type')
    .populate('metadata.eventId', 'title startDate')
    .populate('metadata.itemId', 'title price images');
};

// Static method to get unread count
notificationSchema.statics.getUnreadCount = function(userId, type = null) {
  const query = { recipient: userId, isRead: false };
  if (type) query.type = type;
  
  return this.countDocuments(query);
};

// Static method to mark all as read
notificationSchema.statics.markAllAsRead = function(userId, type = null) {
  const query = { recipient: userId, isRead: false };
  if (type) query.type = type;
  
  return this.updateMany(query, {
    isRead: true,
    readAt: new Date()
  });
};

// Static method to clean up old notifications
notificationSchema.statics.cleanupOldNotifications = function() {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  
  return this.deleteMany({
    createdAt: { $lt: thirtyDaysAgo },
    isRead: true
  });
};

// Static method to send scheduled notifications
notificationSchema.statics.sendScheduledNotifications = async function() {
  const now = new Date();
  
  const scheduledNotifications = await this.find({
    scheduledFor: { $lte: now },
    'deliveryMethod.inApp.sent': false
  });
  
  for (const notification of scheduledNotifications) {
    // Mark as sent
    notification.deliveryMethod.inApp.sent = true;
    notification.deliveryMethod.inApp.sentAt = new Date();
    
    // Here you would emit socket event for real-time delivery
    // io.to(`user-${notification.recipient}`).emit('notification', notification);
    
    await notification.save();
  }
  
  return scheduledNotifications.length;
};

// Static method to create bulk notifications
notificationSchema.statics.createBulkNotifications = async function(recipients, notificationData) {
  const notifications = recipients.map(recipientId => ({
    ...notificationData,
    recipient: recipientId
  }));
  
  return this.insertMany(notifications);
};

// Pre-save middleware to set default values
notificationSchema.pre('save', function(next) {
  // Set default icon based on type
  if (!this.icon) {
    switch (this.type) {
      case 'like': this.icon = '❤️'; break;
      case 'comment': this.icon = '💬'; break;
      case 'follow': this.icon = '👤'; break;
      case 'episode_release': this.icon = '📺'; break;
      case 'event_reminder': this.icon = '🎪'; break;
      case 'price_drop': this.icon = '🏷️'; break;
      case 'clan_promotion': this.icon = '🏆'; break;
      case 'achievement_unlocked': this.icon = '🎖️'; break;
      case 'message': this.icon = '💬'; break;
      default: this.icon = '🔔';
    }
  }
  
  next();
});

export default mongoose.model('Notification', notificationSchema);
