import mongoose from 'mongoose';

const eventSchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  
  // Event Type
  type: {
    type: String,
    enum: ['convention', 'meetup', 'watch-party', 'tournament', 'workshop', 'screening'],
    required: true
  },
  
  // Organizer
  organizer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Date and Time
  startDate: {
    type: Date,
    required: true
  },
  
  endDate: {
    type: Date,
    required: true
  },
  
  timezone: {
    type: String,
    default: 'UTC'
  },
  
  // Location
  location: {
    type: {
      type: String,
      enum: ['online', 'physical'],
      required: true
    },
    venue: String, // Physical address or online platform
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    city: String,
    country: String
  },
  
  // Capacity and Pricing
  capacity: {
    max: {
      type: Number,
      default: 100
    },
    current: {
      type: Number,
      default: 0
    }
  },
  
  pricing: {
    isFree: {
      type: Boolean,
      default: true
    },
    price: {
      type: Number,
      default: 0,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD'
    }
  },
  
  // Anime Related
  animeReference: {
    animeId: String,
    title: String,
    episode: Number,
    season: String
  },
  
  // Watch Party Specific
  watchParty: {
    streamingPlatform: String,
    streamUrl: String,
    episodes: [Number],
    chatRoomId: String,
    syncEnabled: {
      type: Boolean,
      default: true
    }
  },
  
  // Tournament Specific
  tournament: {
    game: String,
    format: {
      type: String,
      enum: ['single-elimination', 'double-elimination', 'round-robin', 'swiss']
    },
    maxParticipants: Number,
    prizes: [{
      position: Number,
      reward: String,
      value: Number
    }],
    rules: String,
    registrationDeadline: Date
  },
  
  // Attendees and RSVPs
  attendees: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    status: {
      type: String,
      enum: ['going', 'interested', 'not-going'],
      required: true
    },
    rsvpDate: {
      type: Date,
      default: Date.now
    },
    checkedIn: {
      type: Boolean,
      default: false
    },
    checkedInAt: Date,
    notes: String
  }],
  
  // Waiting List
  waitingList: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    priority: {
      type: Number,
      default: 0
    }
  }],
  
  // Tags and Categories
  tags: [String],
  
  category: {
    type: String,
    enum: ['anime', 'manga', 'gaming', 'cosplay', 'art', 'music', 'general'],
    default: 'anime'
  },
  
  // Clan Association
  clan: {
    type: String,
    enum: ['Naruto', 'One Piece', 'Demon Slayer', 'Attack on Titan', 'Dragon Ball']
  },
  
  // Media
  images: [{
    url: String,
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  
  // Status and Visibility
  status: {
    type: String,
    enum: ['draft', 'published', 'cancelled', 'completed', 'ongoing'],
    default: 'draft'
  },
  
  visibility: {
    type: String,
    enum: ['public', 'clan', 'friends', 'private'],
    default: 'public'
  },
  
  // Requirements
  requirements: {
    minLevel: {
      type: Number,
      default: 1
    },
    clanOnly: {
      type: Boolean,
      default: false
    },
    ageRestriction: {
      min: Number,
      max: Number
    },
    approvalRequired: {
      type: Boolean,
      default: false
    }
  },
  
  // Notifications and Reminders
  reminders: [{
    type: {
      type: String,
      enum: ['1-week', '1-day', '1-hour', '15-minutes']
    },
    sent: {
      type: Boolean,
      default: false
    },
    sentAt: Date
  }],
  
  // Analytics
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    },
    clickThroughs: {
      type: Number,
      default: 0
    }
  },
  
  // Feedback and Reviews
  feedback: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
eventSchema.index({ startDate: 1 });
eventSchema.index({ type: 1 });
eventSchema.index({ organizer: 1 });
eventSchema.index({ status: 1 });
eventSchema.index({ visibility: 1 });
eventSchema.index({ clan: 1 });
eventSchema.index({ tags: 1 });
eventSchema.index({ 'location.type': 1 });

// Compound indexes
eventSchema.index({ status: 1, visibility: 1, startDate: 1 });
eventSchema.index({ type: 1, startDate: 1 });

// Virtual for attendee count by status
eventSchema.virtual('attendeeCount').get(function() {
  return {
    going: this.attendees.filter(a => a.status === 'going').length,
    interested: this.attendees.filter(a => a.status === 'interested').length,
    notGoing: this.attendees.filter(a => a.status === 'not-going').length,
    total: this.attendees.length
  };
});

// Virtual for is full
eventSchema.virtual('isFull').get(function() {
  const goingCount = this.attendees.filter(a => a.status === 'going').length;
  return goingCount >= this.capacity.max;
});

// Virtual for spots remaining
eventSchema.virtual('spotsRemaining').get(function() {
  const goingCount = this.attendees.filter(a => a.status === 'going').length;
  return Math.max(0, this.capacity.max - goingCount);
});

// Virtual for average rating
eventSchema.virtual('averageRating').get(function() {
  if (this.feedback.length === 0) return 0;
  const sum = this.feedback.reduce((acc, f) => acc + f.rating, 0);
  return Math.round((sum / this.feedback.length) * 10) / 10;
});

// Method to check if user is attending
eventSchema.methods.isUserAttending = function(userId, status = 'going') {
  return this.attendees.some(attendee => 
    attendee.user.toString() === userId.toString() && attendee.status === status
  );
};

// Method to add/update RSVP
eventSchema.methods.updateRSVP = function(userId, status, notes = '') {
  const existingRSVP = this.attendees.find(attendee => 
    attendee.user.toString() === userId.toString()
  );
  
  if (existingRSVP) {
    existingRSVP.status = status;
    existingRSVP.notes = notes;
    existingRSVP.rsvpDate = new Date();
  } else {
    this.attendees.push({
      user: userId,
      status: status,
      notes: notes
    });
  }
  
  // Update capacity
  this.capacity.current = this.attendees.filter(a => a.status === 'going').length;
  
  return this.save();
};

// Method to add to waiting list
eventSchema.methods.addToWaitingList = function(userId) {
  const isAlreadyWaiting = this.waitingList.some(w => w.user.toString() === userId.toString());
  
  if (!isAlreadyWaiting) {
    this.waitingList.push({
      user: userId,
      priority: this.waitingList.length
    });
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Method to check in user
eventSchema.methods.checkInUser = function(userId) {
  const attendee = this.attendees.find(a => 
    a.user.toString() === userId.toString() && a.status === 'going'
  );
  
  if (attendee) {
    attendee.checkedIn = true;
    attendee.checkedInAt = new Date();
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Method to increment views
eventSchema.methods.incrementViews = function() {
  this.analytics.views += 1;
  return this.save();
};

// Static method to get upcoming events
eventSchema.statics.getUpcoming = function(limit = 10) {
  return this.find({
    status: 'published',
    visibility: 'public',
    startDate: { $gte: new Date() }
  })
  .sort({ startDate: 1 })
  .limit(limit)
  .populate('organizer', 'username avatar clan');
};

// Static method to get events by type
eventSchema.statics.getByType = function(type, limit = 20) {
  return this.find({
    type: type,
    status: 'published',
    visibility: 'public',
    startDate: { $gte: new Date() }
  })
  .sort({ startDate: 1 })
  .limit(limit)
  .populate('organizer', 'username avatar clan');
};

// Static method to get clan events
eventSchema.statics.getByClan = function(clan, limit = 20) {
  return this.find({
    clan: clan,
    status: 'published',
    visibility: { $in: ['public', 'clan'] },
    startDate: { $gte: new Date() }
  })
  .sort({ startDate: 1 })
  .limit(limit)
  .populate('organizer', 'username avatar clan');
};

// Pre-save middleware to update timestamps
eventSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

export default mongoose.model('Event', eventSchema);
