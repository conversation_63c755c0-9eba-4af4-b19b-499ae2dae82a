{"name": "event-service", "version": "1.0.0", "description": "AnimeVerse Event Service - Events, RSVP, calendar", "main": "app.js", "type": "module", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "cross-env NODE_ENV=test jest --runInBand --detectOpenHandles", "test:watch": "npm test -- --watch", "test:coverage": "npm test -- --coverage"}, "keywords": ["anime", "events", "rsvp", "microservice"], "author": "AnimeVerse Team", "license": "MIT", "dependencies": {"express": "^5.1.0", "mongoose": "^8.1.1", "cors": "^2.8.5", "helmet": "^8.1.0", "morgan": "^1.10.0", "dotenv": "^17.2.0", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "winston": "^3.17.0", "axios": "^1.10.0"}, "devDependencies": {"nodemon": "^3.1.10", "jest": "^30.0.4", "supertest": "^7.1.3", "cross-env": "^7.0.3"}}