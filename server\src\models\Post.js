import mongoose from 'mongoose';

const postSchema = new mongoose.Schema({
  // Author Information
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Content
  content: {
    type: String,
    required: true,
    maxlength: 2000
  },
  
  // Media
  images: [{
    url: String,
    caption: String,
    alt: String
  }],
  
  // Anime Related
  animeReference: {
    animeId: String,
    title: String,
    episode: Number,
    character: String
  },
  
  // Post Type
  type: {
    type: String,
    enum: ['text', 'image', 'review', 'discussion', 'fanart', 'news'],
    default: 'text'
  },
  
  // Review specific fields (if type is 'review')
  review: {
    animeId: String,
    animeTitle: String,
    rating: {
      type: Number,
      min: 1,
      max: 10
    },
    pros: [String],
    cons: [String],
    recommendation: {
      type: String,
      enum: ['highly_recommended', 'recommended', 'mixed', 'not_recommended']
    }
  },
  
  // Engagement
  likes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    likedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  comments: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: {
      type: String,
      required: true,
      maxlength: 500
    },
    likes: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      likedAt: {
        type: Date,
        default: Date.now
      }
    }],
    replies: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      content: {
        type: String,
        required: true,
        maxlength: 300
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }],
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  shares: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    sharedAt: {
      type: Date,
      default: Date.now
    },
    comment: String // Optional comment when sharing
  }],
  
  // Tags and Categories
  tags: [String],
  
  // Visibility and Status
  visibility: {
    type: String,
    enum: ['public', 'friends', 'clan', 'private'],
    default: 'public'
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  isPinned: {
    type: Boolean,
    default: false
  },
  
  // Moderation
  isReported: {
    type: Boolean,
    default: false
  },
  
  reports: [{
    reporter: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: {
      type: String,
      enum: ['spam', 'harassment', 'inappropriate', 'copyright', 'other']
    },
    description: String,
    reportedAt: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['pending', 'reviewed', 'resolved', 'dismissed'],
      default: 'pending'
    }
  }],
  
  // Analytics
  views: {
    type: Number,
    default: 0
  },
  
  viewedBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    viewedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
postSchema.index({ author: 1 });
postSchema.index({ createdAt: -1 });
postSchema.index({ type: 1 });
postSchema.index({ visibility: 1 });
postSchema.index({ isActive: 1 });
postSchema.index({ tags: 1 });
postSchema.index({ 'animeReference.animeId': 1 });

// Compound indexes
postSchema.index({ author: 1, createdAt: -1 });
postSchema.index({ visibility: 1, isActive: 1, createdAt: -1 });

// Virtual for like count
postSchema.virtual('likeCount').get(function() {
  return this.likes.length;
});

// Virtual for comment count
postSchema.virtual('commentCount').get(function() {
  return this.comments.length;
});

// Virtual for share count
postSchema.virtual('shareCount').get(function() {
  return this.shares.length;
});

// Virtual for engagement score
postSchema.virtual('engagementScore').get(function() {
  return (this.likes.length * 1) + (this.comments.length * 2) + (this.shares.length * 3);
});

// Method to check if user liked the post
postSchema.methods.isLikedBy = function(userId) {
  return this.likes.some(like => like.user.toString() === userId.toString());
};

// Method to add like
postSchema.methods.addLike = function(userId) {
  if (!this.isLikedBy(userId)) {
    this.likes.push({ user: userId });
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to remove like
postSchema.methods.removeLike = function(userId) {
  this.likes = this.likes.filter(like => like.user.toString() !== userId.toString());
  return this.save();
};

// Method to add comment
postSchema.methods.addComment = function(userId, content) {
  this.comments.push({
    user: userId,
    content: content
  });
  return this.save();
};

// Method to increment views
postSchema.methods.incrementViews = function(userId) {
  // Only count unique views
  const hasViewed = this.viewedBy.some(view => view.user.toString() === userId.toString());
  if (!hasViewed) {
    this.views += 1;
    this.viewedBy.push({ user: userId });
    return this.save();
  }
  return Promise.resolve(this);
};

// Static method to get trending posts
postSchema.statics.getTrending = function(limit = 10) {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        isActive: true,
        visibility: 'public',
        createdAt: { $gte: oneDayAgo }
      }
    },
    {
      $addFields: {
        engagementScore: {
          $add: [
            { $size: '$likes' },
            { $multiply: [{ $size: '$comments' }, 2] },
            { $multiply: [{ $size: '$shares' }, 3] }
          ]
        }
      }
    },
    {
      $sort: { engagementScore: -1, createdAt: -1 }
    },
    {
      $limit: limit
    }
  ]);
};

// Static method to get posts by clan
postSchema.statics.getByClan = function(clan, limit = 20) {
  return this.find({
    isActive: true,
    visibility: { $in: ['public', 'clan'] }
  })
  .populate({
    path: 'author',
    match: { clan: clan },
    select: 'username avatar clan clanRank'
  })
  .sort({ createdAt: -1 })
  .limit(limit);
};

// Pre-save middleware to update timestamps
postSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

export default mongoose.model('Post', postSchema);
