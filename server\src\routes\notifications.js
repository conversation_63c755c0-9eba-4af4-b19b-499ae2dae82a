import express from 'express';
import Notification from '../models/Notification.js';
import { catchAsync, notFoundError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get user notifications
router.get('/', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 50, 
    type, 
    isRead 
  } = req.query;
  
  const options = {
    limit: parseInt(limit),
    skip: (page - 1) * limit,
    type,
    isRead: isRead !== undefined ? isRead === 'true' : null
  };
  
  const notifications = await Notification.getUserNotifications(req.user._id, options);
  const total = await Notification.countDocuments({
    recipient: req.user._id,
    ...(type && { type }),
    ...(isRead !== undefined && { isRead: isRead === 'true' })
  });
  
  res.json({
    success: true,
    notifications,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Get unread count
router.get('/unread-count', catchAsync(async (req, res) => {
  const { type } = req.query;
  
  const count = await Notification.getUnreadCount(req.user._id, type);
  
  res.json({
    success: true,
    unreadCount: count
  });
}));

// Mark notification as read
router.patch('/:notificationId/read', catchAsync(async (req, res) => {
  const { notificationId } = req.params;
  
  const notification = await Notification.findById(notificationId);
  
  if (!notification) {
    throw notFoundError('Notification');
  }
  
  if (notification.recipient.toString() !== req.user._id.toString()) {
    throw new AppError('Access denied', 403);
  }
  
  await notification.markAsRead();
  
  res.json({
    success: true,
    message: 'Notification marked as read'
  });
}));

// Mark all notifications as read
router.patch('/mark-all-read', catchAsync(async (req, res) => {
  const { type } = req.body;
  
  await Notification.markAllAsRead(req.user._id, type);
  
  res.json({
    success: true,
    message: 'All notifications marked as read'
  });
}));

// Delete notification
router.delete('/:notificationId', catchAsync(async (req, res) => {
  const { notificationId } = req.params;
  
  const notification = await Notification.findById(notificationId);
  
  if (!notification) {
    throw notFoundError('Notification');
  }
  
  if (notification.recipient.toString() !== req.user._id.toString()) {
    throw new AppError('Access denied', 403);
  }
  
  await notification.deleteOne();
  
  res.json({
    success: true,
    message: 'Notification deleted'
  });
}));

export default router;
