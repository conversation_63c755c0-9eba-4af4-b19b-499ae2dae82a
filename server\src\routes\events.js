import express from 'express';
import { body, validationResult } from 'express-validator';
import Event from '../models/Event.js';
import Notification from '../models/Notification.js';
import { catchAsync, AppError, notFoundError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get events
router.get('/', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    type, 
    clan, 
    upcoming = true 
  } = req.query;
  
  const query = {
    status: 'published',
    visibility: { $in: ['public', 'clan'] }
  };
  
  if (type) query.type = type;
  if (clan) query.clan = clan;
  if (upcoming === 'true') {
    query.startDate = { $gte: new Date() };
  }
  
  const events = await Event.find(query)
    .populate('organizer', 'username avatar clan')
    .sort({ startDate: 1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));
  
  const total = await Event.countDocuments(query);
  
  res.json({
    success: true,
    events,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Create event
router.post('/', [
  body('title').isLength({ min: 1, max: 200 }),
  body('description').isLength({ min: 1, max: 2000 }),
  body('type').isIn(['convention', 'meetup', 'watch-party', 'tournament', 'workshop', 'screening']),
  body('startDate').isISO8601(),
  body('endDate').isISO8601()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const eventData = {
    ...req.body,
    organizer: req.user._id
  };
  
  const event = new Event(eventData);
  await event.save();
  await event.populate('organizer', 'username avatar clan');
  
  res.status(201).json({
    success: true,
    message: 'Event created successfully',
    event
  });
}));

// Get single event
router.get('/:eventId', catchAsync(async (req, res) => {
  const { eventId } = req.params;
  
  const event = await Event.findById(eventId)
    .populate('organizer', 'username avatar clan')
    .populate('attendees.user', 'username avatar clan');
  
  if (!event) {
    throw notFoundError('Event');
  }
  
  // Increment views
  await event.incrementViews();
  
  const userRSVP = event.attendees.find(attendee => 
    attendee.user._id.toString() === req.user._id.toString()
  );
  
  res.json({
    success: true,
    event,
    userRSVP: userRSVP ? userRSVP.status : null
  });
}));

// RSVP to event
router.post('/:eventId/rsvp', [
  body('status').isIn(['going', 'interested', 'not-going'])
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { eventId } = req.params;
  const { status, notes = '' } = req.body;
  
  const event = await Event.findById(eventId);
  if (!event) {
    throw notFoundError('Event');
  }
  
  await event.updateRSVP(req.user._id, status, notes);
  
  // Create notification for organizer
  if (status === 'going' && event.organizer.toString() !== req.user._id.toString()) {
    await Notification.createNotification({
      recipient: event.organizer,
      sender: req.user._id,
      type: 'event_rsvp',
      title: 'New Event RSVP',
      message: `${req.user.username} is attending your event: ${event.title}`,
      actionUrl: `/events/${eventId}`,
      metadata: { eventId }
    });
  }
  
  res.json({
    success: true,
    message: `RSVP updated to ${status}`,
    status
  });
}));

// Get user's events
router.get('/user/:userId', catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { type = 'attending' } = req.query; // attending, organizing, interested
  
  let query = {};
  
  if (type === 'organizing') {
    query = { organizer: userId };
  } else {
    query = {
      'attendees.user': userId,
      'attendees.status': type === 'interested' ? 'interested' : 'going'
    };
  }
  
  const events = await Event.find(query)
    .populate('organizer', 'username avatar clan')
    .sort({ startDate: 1 });
  
  res.json({
    success: true,
    events
  });
}));

export default router;
