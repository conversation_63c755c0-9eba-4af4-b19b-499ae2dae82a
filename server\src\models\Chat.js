import mongoose from 'mongoose';

const messageSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  content: {
    type: String,
    required: true,
    maxlength: 2000
  },
  
  type: {
    type: String,
    enum: ['text', 'image', 'file', 'voice', 'video', 'system'],
    default: 'text'
  },
  
  // Media attachments
  attachments: [{
    type: {
      type: String,
      enum: ['image', 'file', 'voice', 'video']
    },
    url: String,
    filename: String,
    size: Number,
    mimeType: String,
    duration: Number // For voice/video messages
  }],
  
  // Message reactions
  reactions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    emoji: String,
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Reply to another message
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  },
  
  // Message status
  isEdited: {
    type: Boolean,
    default: false
  },
  
  editedAt: Date,
  
  isDeleted: {
    type: Boolean,
    default: false
  },
  
  deletedAt: Date,
  
  // Read receipts
  readBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const chatSchema = new mongoose.Schema({
  // Chat type
  type: {
    type: String,
    enum: ['direct', 'group', 'clan'],
    required: true
  },
  
  // Participants
  participants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['member', 'admin', 'owner'],
      default: 'member'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    lastSeen: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    },
    // Notification settings for this chat
    notifications: {
      muted: {
        type: Boolean,
        default: false
      },
      mutedUntil: Date
    }
  }],
  
  // Group/Clan chat specific
  name: String,
  description: String,
  avatar: String,
  
  // Clan association
  clan: {
    type: String,
    enum: ['Naruto', 'One Piece', 'Demon Slayer', 'Attack on Titan', 'Dragon Ball']
  },
  
  // Messages
  messages: [messageSchema],
  
  // Last message for quick access
  lastMessage: {
    content: String,
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: Date,
    type: String
  },
  
  // Chat settings
  settings: {
    // Who can add members
    whoCanAddMembers: {
      type: String,
      enum: ['everyone', 'admins', 'owner'],
      default: 'everyone'
    },
    
    // Who can send messages
    whoCanSendMessages: {
      type: String,
      enum: ['everyone', 'admins', 'owner'],
      default: 'everyone'
    },
    
    // Message retention
    messageRetentionDays: {
      type: Number,
      default: 0 // 0 means forever
    },
    
    // Auto-delete messages
    autoDeleteEnabled: {
      type: Boolean,
      default: false
    }
  },
  
  // Voice/Video call history
  calls: [{
    type: {
      type: String,
      enum: ['voice', 'video']
    },
    initiator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    participants: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      joinedAt: Date,
      leftAt: Date
    }],
    startedAt: {
      type: Date,
      default: Date.now
    },
    endedAt: Date,
    duration: Number, // in seconds
    status: {
      type: String,
      enum: ['ongoing', 'ended', 'missed', 'declined'],
      default: 'ongoing'
    }
  }],
  
  // Chat status
  isActive: {
    type: Boolean,
    default: true
  },
  
  isArchived: {
    type: Boolean,
    default: false
  },
  
  archivedAt: Date,
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
chatSchema.index({ type: 1 });
chatSchema.index({ 'participants.user': 1 });
chatSchema.index({ clan: 1 });
chatSchema.index({ isActive: 1 });
chatSchema.index({ updatedAt: -1 });
chatSchema.index({ 'messages.createdAt': -1 });

// Compound indexes
chatSchema.index({ type: 1, isActive: 1, updatedAt: -1 });
chatSchema.index({ 'participants.user': 1, isActive: 1 });

// Virtual for participant count
chatSchema.virtual('participantCount').get(function() {
  return this.participants.filter(p => p.isActive).length;
});

// Virtual for unread message count for a user
chatSchema.virtual('getUnreadCount').get(function() {
  return function(userId) {
    const participant = this.participants.find(p => p.user.toString() === userId.toString());
    if (!participant) return 0;
    
    return this.messages.filter(msg => 
      msg.createdAt > participant.lastSeen && 
      msg.sender.toString() !== userId.toString() &&
      !msg.isDeleted
    ).length;
  }.bind(this);
});

// Method to add participant
chatSchema.methods.addParticipant = function(userId, role = 'member') {
  const existingParticipant = this.participants.find(p => p.user.toString() === userId.toString());
  
  if (existingParticipant) {
    existingParticipant.isActive = true;
    existingParticipant.joinedAt = new Date();
  } else {
    this.participants.push({
      user: userId,
      role: role
    });
  }
  
  return this.save();
};

// Method to remove participant
chatSchema.methods.removeParticipant = function(userId) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  
  if (participant) {
    participant.isActive = false;
  }
  
  return this.save();
};

// Method to add message
chatSchema.methods.addMessage = function(senderId, content, type = 'text', attachments = []) {
  const message = {
    sender: senderId,
    content: content,
    type: type,
    attachments: attachments
  };
  
  this.messages.push(message);
  
  // Update last message
  this.lastMessage = {
    content: content,
    sender: senderId,
    timestamp: new Date(),
    type: type
  };
  
  this.updatedAt = new Date();
  
  return this.save();
};

// Method to mark messages as read
chatSchema.methods.markAsRead = function(userId, messageId = null) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  
  if (participant) {
    participant.lastSeen = new Date();
    
    // If specific message ID provided, mark that message as read
    if (messageId) {
      const message = this.messages.id(messageId);
      if (message && !message.readBy.some(r => r.user.toString() === userId.toString())) {
        message.readBy.push({ user: userId });
      }
    }
  }
  
  return this.save();
};

// Method to add reaction to message
chatSchema.methods.addReaction = function(messageId, userId, emoji) {
  const message = this.messages.id(messageId);
  
  if (message) {
    // Remove existing reaction from this user
    message.reactions = message.reactions.filter(r => r.user.toString() !== userId.toString());
    
    // Add new reaction
    message.reactions.push({
      user: userId,
      emoji: emoji
    });
  }
  
  return this.save();
};

// Method to check if user is participant
chatSchema.methods.isParticipant = function(userId) {
  return this.participants.some(p => 
    p.user.toString() === userId.toString() && p.isActive
  );
};

// Method to get participant role
chatSchema.methods.getParticipantRole = function(userId) {
  const participant = this.participants.find(p => 
    p.user.toString() === userId.toString() && p.isActive
  );
  return participant ? participant.role : null;
};

// Static method to find direct chat between two users
chatSchema.statics.findDirectChat = function(user1Id, user2Id) {
  return this.findOne({
    type: 'direct',
    isActive: true,
    'participants.user': { $all: [user1Id, user2Id] },
    'participants.isActive': true
  });
};

// Static method to get user's chats
chatSchema.statics.getUserChats = function(userId, limit = 50) {
  return this.find({
    'participants.user': userId,
    'participants.isActive': true,
    isActive: true
  })
  .sort({ updatedAt: -1 })
  .limit(limit)
  .populate('participants.user', 'username avatar clan isOnline lastSeen')
  .populate('lastMessage.sender', 'username avatar');
};

// Static method to get clan chats
chatSchema.statics.getClanChats = function(clan) {
  return this.find({
    type: 'clan',
    clan: clan,
    isActive: true
  })
  .sort({ updatedAt: -1 })
  .populate('participants.user', 'username avatar clan');
};

// Pre-save middleware to update timestamps
chatSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

export default mongoose.model('Chat', chatSchema);
