#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔄 AnimeVerse Microservices Migration Tool');
console.log('==========================================\n');

// Check if server directory exists
const serverDir = path.join(__dirname, 'server');
const userServiceDir = path.join(__dirname, 'User-Service');
const servicesDir = path.join(__dirname, 'services');

if (!fs.existsSync(serverDir)) {
  console.log('✅ No monolithic server found - microservices architecture is ready!');
  process.exit(0);
}

console.log('📁 Found monolithic server directory');
console.log('🔧 Migrating to microservices architecture...\n');

// Migration steps
const migrationSteps = [
  {
    name: 'Backup monolithic server',
    action: () => {
      const backupDir = path.join(__dirname, 'server-backup');
      if (!fs.existsSync(backupDir)) {
        fs.renameSync(serverDir, backupDir);
        console.log('✅ Monolithic server backed up to server-backup/');
      } else {
        console.log('⚠️  Backup already exists, skipping...');
      }
    }
  },
  {
    name: 'Verify User-Service exists',
    action: () => {
      if (fs.existsSync(userServiceDir)) {
        console.log('✅ User-Service found and ready');
      } else {
        console.log('❌ User-Service not found! Please ensure it exists.');
        process.exit(1);
      }
    }
  },
  {
    name: 'Verify microservices structure',
    action: () => {
      const requiredServices = [
        'API-Gateway',
        'Social-Service',
        'Event-Service',
        'Marketplace-Service',
        'Chat-Service',
        'Anime-Service'
      ];
      
      let allServicesExist = true;
      
      requiredServices.forEach(service => {
        const servicePath = path.join(servicesDir, service);
        if (fs.existsSync(servicePath)) {
          console.log(`✅ ${service} found`);
        } else {
          console.log(`❌ ${service} missing`);
          allServicesExist = false;
        }
      });
      
      if (!allServicesExist) {
        console.log('\n❌ Some microservices are missing. Please run the setup first.');
        process.exit(1);
      }
    }
  },
  {
    name: 'Update client configuration',
    action: () => {
      const clientEnvPath = path.join(__dirname, 'client', '.env');
      const clientEnvExamplePath = path.join(__dirname, 'client', '.env.example');
      
      // Update .env.example
      const envContent = `# AnimeVerse Client Configuration
VITE_API_URL=http://localhost:3000/api
VITE_SOCKET_URL=http://localhost:3005
VITE_APP_NAME=AnimeVerse
VITE_APP_VERSION=2.0.0
`;
      
      fs.writeFileSync(clientEnvExamplePath, envContent);
      
      if (!fs.existsSync(clientEnvPath)) {
        fs.writeFileSync(clientEnvPath, envContent);
      }
      
      console.log('✅ Client configuration updated for microservices');
    }
  },
  {
    name: 'Create shared utilities',
    action: () => {
      const sharedDir = path.join(__dirname, 'shared');
      if (fs.existsSync(sharedDir)) {
        console.log('✅ Shared utilities already exist');
      } else {
        console.log('⚠️  Shared utilities not found - they should have been created');
      }
    }
  }
];

// Execute migration steps
async function runMigration() {
  for (const step of migrationSteps) {
    console.log(`🔄 ${step.name}...`);
    try {
      await step.action();
    } catch (error) {
      console.error(`❌ Failed: ${step.name}`);
      console.error(error.message);
      process.exit(1);
    }
  }
  
  console.log('\n🎉 Migration completed successfully!');
  console.log('\n📋 Next Steps:');
  console.log('1. Install dependencies: npm run install:all');
  console.log('2. Set up environment files for each service');
  console.log('3. Start MongoDB: docker run -d -p 27017:27017 mongo:7.0');
  console.log('4. Initialize database: npm run init:db');
  console.log('5. Start all services: npm run dev');
  console.log('\n🌐 Services will be available at:');
  console.log('   - API Gateway: http://localhost:3000');
  console.log('   - Frontend: http://localhost:5173');
  console.log('\n📚 See MICROSERVICES-README.md for detailed documentation');
}

// Run migration
runMigration().catch(console.error);
