import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import Chat from '../models/Chat.js';
import Notification from '../models/Notification.js';

let io;
const connectedUsers = new Map(); // userId -> socketId
const userSockets = new Map(); // socketId -> userId

export const initializeSocket = (socketIO) => {
  io = socketIO;
  
  // Authentication middleware for socket connections
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }
      
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'animeverse_secret_key');
      const user = await User.findById(decoded.userId).select('-password');
      
      if (!user || !user.isActive) {
        return next(new Error('Invalid user'));
      }
      
      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });
  
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.username} (${socket.id})`);
    
    // Store user connection
    connectedUsers.set(socket.userId, socket.id);
    userSockets.set(socket.id, socket.userId);
    
    // Update user online status
    updateUserOnlineStatus(socket.userId, true);
    
    // Join user to their personal room for notifications
    socket.join(`user-${socket.userId}`);
    
    // Join user to their clan room
    if (socket.user.clan) {
      socket.join(`clan-${socket.user.clan}`);
    }
    
    // Handle chat events
    setupChatHandlers(socket);
    
    // Handle notification events
    setupNotificationHandlers(socket);
    
    // Handle typing events
    setupTypingHandlers(socket);
    
    // Handle call events
    setupCallHandlers(socket);
    
    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`User disconnected: ${socket.user.username} (${socket.id})`);
      
      // Remove user connection
      connectedUsers.delete(socket.userId);
      userSockets.delete(socket.id);
      
      // Update user online status
      updateUserOnlineStatus(socket.userId, false);
      
      // Notify others about user going offline
      socket.broadcast.emit('user-offline', {
        userId: socket.userId,
        username: socket.user.username
      });
    });
    
    // Notify others about user coming online
    socket.broadcast.emit('user-online', {
      userId: socket.userId,
      username: socket.user.username,
      clan: socket.user.clan
    });
  });
};

// Chat event handlers
const setupChatHandlers = (socket) => {
  // Join chat room
  socket.on('join-chat', async (chatId) => {
    try {
      const chat = await Chat.findById(chatId);
      
      if (!chat || !chat.isParticipant(socket.userId)) {
        socket.emit('error', { message: 'Access denied to chat' });
        return;
      }
      
      socket.join(`chat-${chatId}`);
      socket.emit('joined-chat', { chatId });
    } catch (error) {
      socket.emit('error', { message: 'Failed to join chat' });
    }
  });
  
  // Leave chat room
  socket.on('leave-chat', (chatId) => {
    socket.leave(`chat-${chatId}`);
    socket.emit('left-chat', { chatId });
  });
  
  // Send message
  socket.on('send-message', async (data) => {
    try {
      const { chatId, content, type = 'text', attachments = [] } = data;
      
      const chat = await Chat.findById(chatId);
      
      if (!chat || !chat.isParticipant(socket.userId)) {
        socket.emit('error', { message: 'Access denied to chat' });
        return;
      }
      
      // Add message to chat
      await chat.addMessage(socket.userId, content, type, attachments);
      
      // Populate sender info
      await chat.populate('messages.sender', 'username avatar clan');
      const newMessage = chat.messages[chat.messages.length - 1];
      
      // Emit to all participants in the chat
      io.to(`chat-${chatId}`).emit('new-message', {
        chatId,
        message: newMessage
      });
      
      // Send push notifications to offline users
      const offlineParticipants = chat.participants.filter(p => 
        p.isActive && 
        p.user.toString() !== socket.userId && 
        !connectedUsers.has(p.user.toString())
      );
      
      for (const participant of offlineParticipants) {
        await Notification.createNotification({
          recipient: participant.user,
          sender: socket.userId,
          type: 'message',
          title: `New message from ${socket.user.username}`,
          message: content.length > 50 ? content.substring(0, 50) + '...' : content,
          actionUrl: `/sensei-chat?chat=${chatId}`,
          metadata: { chatId }
        });
      }
      
    } catch (error) {
      socket.emit('error', { message: 'Failed to send message' });
    }
  });
  
  // Mark messages as read
  socket.on('mark-read', async (data) => {
    try {
      const { chatId, messageId } = data;
      
      const chat = await Chat.findById(chatId);
      
      if (chat && chat.isParticipant(socket.userId)) {
        await chat.markAsRead(socket.userId, messageId);
        
        // Notify other participants
        socket.to(`chat-${chatId}`).emit('message-read', {
          chatId,
          messageId,
          userId: socket.userId
        });
      }
    } catch (error) {
      console.error('Mark read error:', error);
    }
  });
};

// Notification event handlers
const setupNotificationHandlers = (socket) => {
  // Mark notification as read
  socket.on('mark-notification-read', async (notificationId) => {
    try {
      const notification = await Notification.findById(notificationId);
      
      if (notification && notification.recipient.toString() === socket.userId) {
        await notification.markAsRead();
        socket.emit('notification-read', { notificationId });
      }
    } catch (error) {
      console.error('Mark notification read error:', error);
    }
  });
  
  // Mark all notifications as read
  socket.on('mark-all-notifications-read', async () => {
    try {
      await Notification.markAllAsRead(socket.userId);
      socket.emit('all-notifications-read');
    } catch (error) {
      console.error('Mark all notifications read error:', error);
    }
  });
};

// Typing event handlers
const setupTypingHandlers = (socket) => {
  // User started typing
  socket.on('typing-start', (data) => {
    const { chatId } = data;
    socket.to(`chat-${chatId}`).emit('user-typing', {
      chatId,
      userId: socket.userId,
      username: socket.user.username
    });
  });
  
  // User stopped typing
  socket.on('typing-stop', (data) => {
    const { chatId } = data;
    socket.to(`chat-${chatId}`).emit('user-stopped-typing', {
      chatId,
      userId: socket.userId
    });
  });
};

// Call event handlers
const setupCallHandlers = (socket) => {
  // Initiate call
  socket.on('initiate-call', async (data) => {
    const { chatId, type = 'voice' } = data; // voice or video
    
    try {
      const chat = await Chat.findById(chatId);
      
      if (!chat || !chat.isParticipant(socket.userId)) {
        socket.emit('call-error', { message: 'Access denied' });
        return;
      }
      
      // Add call to chat history
      const call = {
        type,
        initiator: socket.userId,
        participants: [{ user: socket.userId, joinedAt: new Date() }]
      };
      
      chat.calls.push(call);
      await chat.save();
      
      const callId = chat.calls[chat.calls.length - 1]._id;
      
      // Notify other participants
      socket.to(`chat-${chatId}`).emit('incoming-call', {
        callId,
        chatId,
        type,
        initiator: {
          id: socket.userId,
          username: socket.user.username,
          avatar: socket.user.avatar
        }
      });
      
      socket.emit('call-initiated', { callId, chatId, type });
      
    } catch (error) {
      socket.emit('call-error', { message: 'Failed to initiate call' });
    }
  });
  
  // Join call
  socket.on('join-call', async (data) => {
    const { callId, chatId } = data;
    
    socket.join(`call-${callId}`);
    socket.to(`call-${callId}`).emit('user-joined-call', {
      userId: socket.userId,
      username: socket.user.username
    });
  });
  
  // Leave call
  socket.on('leave-call', (data) => {
    const { callId } = data;
    
    socket.leave(`call-${callId}`);
    socket.to(`call-${callId}`).emit('user-left-call', {
      userId: socket.userId
    });
  });
};

// Update user online status
const updateUserOnlineStatus = async (userId, isOnline) => {
  try {
    await User.findByIdAndUpdate(userId, {
      isOnline,
      lastSeen: new Date()
    });
  } catch (error) {
    console.error('Failed to update user online status:', error);
  }
};

// Utility functions for sending notifications
export const sendNotificationToUser = async (userId, notification) => {
  const socketId = connectedUsers.get(userId.toString());
  
  if (socketId) {
    io.to(socketId).emit('notification', notification);
  }
};

export const sendNotificationToClan = async (clan, notification, excludeUserId = null) => {
  const roomName = `clan-${clan}`;
  
  if (excludeUserId) {
    const excludeSocketId = connectedUsers.get(excludeUserId.toString());
    if (excludeSocketId) {
      io.to(roomName).except(excludeSocketId).emit('notification', notification);
    } else {
      io.to(roomName).emit('notification', notification);
    }
  } else {
    io.to(roomName).emit('notification', notification);
  }
};

export const broadcastToAllUsers = (event, data) => {
  io.emit(event, data);
};

export const getConnectedUsers = () => {
  return Array.from(connectedUsers.keys());
};

export const isUserOnline = (userId) => {
  return connectedUsers.has(userId.toString());
};

export default { initializeSocket, sendNotificationToUser, sendNotificationToClan, broadcastToAllUsers };
