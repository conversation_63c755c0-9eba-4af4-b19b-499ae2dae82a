import express from 'express';
import Clan from '../models/Clan.js';
import User from '../models/User.js';
import { catchAsync, notFoundError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get all clans
router.get('/', catchAsync(async (req, res) => {
  const clans = await Clan.find({})
    .sort({ 'stats.totalMembers': -1 });
  
  res.json({
    success: true,
    clans
  });
}));

// Get specific clan
router.get('/:clanName', catchAsync(async (req, res) => {
  const { clanName } = req.params;
  
  const clan = await Clan.findOne({ name: clanName });
  if (!clan) {
    throw notFoundError('Clan');
  }
  
  // Update clan statistics
  await clan.updateMemberStats();
  await clan.updateLeaderboards();
  
  res.json({
    success: true,
    clan
  });
}));

// Get clan members
router.get('/:clanName/members', catchAsync(async (req, res) => {
  const { clanName } = req.params;
  const { page = 1, limit = 20 } = req.query;
  
  const members = await User.find({ 
    clan: clanName, 
    isActive: true 
  })
  .select('username avatar level clanPoints clanRank lastSeen isOnline')
  .sort({ clanPoints: -1 })
  .skip((page - 1) * limit)
  .limit(parseInt(limit));
  
  const total = await User.countDocuments({ clan: clanName, isActive: true });
  
  res.json({
    success: true,
    members,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Get clan activities
router.get('/:clanName/activities', catchAsync(async (req, res) => {
  const { clanName } = req.params;
  
  const clan = await Clan.findOne({ name: clanName });
  if (!clan) {
    throw notFoundError('Clan');
  }
  
  const activities = clan.activities
    .filter(activity => activity.isActive)
    .sort((a, b) => b.createdAt - a.createdAt);
  
  res.json({
    success: true,
    activities
  });
}));

export default router;
