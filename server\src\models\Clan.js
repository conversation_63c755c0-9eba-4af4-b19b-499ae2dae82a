import mongoose from 'mongoose';

const clanSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: true,
    unique: true,
    enum: ['Na<PERSON>to', 'One Piece', 'Demon Slayer', 'Attack on Titan', 'Dragon Ball']
  },
  
  description: {
    type: String,
    required: true
  },
  
  icon: {
    type: String,
    required: true
  },
  
  color: {
    primary: String,
    secondary: String,
    accent: String
  },
  
  // Hierarchy and Ranks
  ranks: [{
    name: String,
    level: Number,
    pointsRequired: Number,
    permissions: [String]
  }],
  
  // Statistics
  stats: {
    totalMembers: {
      type: Number,
      default: 0
    },
    activeMembers: {
      type: Number,
      default: 0
    },
    totalPoints: {
      type: Number,
      default: 0
    },
    averageLevel: {
      type: Number,
      default: 1
    }
  },
  
  // Activities and Challenges
  activities: [{
    id: {
      type: mongoose.Schema.Types.ObjectId,
      default: () => new mongoose.Types.ObjectId()
    },
    title: {
      type: String,
      required: true
    },
    description: String,
    type: {
      type: String,
      enum: ['challenge', 'tournament', 'event', 'quest'],
      required: true
    },
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard', 'legendary'],
      default: 'medium'
    },
    rewards: {
      points: Number,
      experience: Number,
      achievements: [String],
      items: [String]
    },
    requirements: {
      minLevel: { type: Number, default: 1 },
      minRank: String,
      prerequisites: [String]
    },
    participants: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      joinedAt: {
        type: Date,
        default: Date.now
      },
      status: {
        type: String,
        enum: ['active', 'completed', 'failed', 'abandoned'],
        default: 'active'
      },
      progress: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
      },
      completedAt: Date
    }],
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: Date,
    isActive: {
      type: Boolean,
      default: true
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Leaderboards
  leaderboards: {
    topContributors: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      points: Number,
      rank: Number
    }],
    mostActive: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      activityScore: Number,
      rank: Number
    }],
    achievements: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      achievementCount: Number,
      rank: Number
    }]
  },
  
  // Recent Activity
  recentActivity: [{
    type: {
      type: String,
      enum: ['member_joined', 'member_promoted', 'challenge_completed', 'achievement_unlocked', 'event_created']
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    description: String,
    metadata: mongoose.Schema.Types.Mixed,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Settings
  settings: {
    isPublic: {
      type: Boolean,
      default: true
    },
    allowJoining: {
      type: Boolean,
      default: true
    },
    requireApproval: {
      type: Boolean,
      default: false
    },
    maxMembers: {
      type: Number,
      default: 10000
    }
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
clanSchema.index({ name: 1 });
clanSchema.index({ 'stats.totalMembers': -1 });
clanSchema.index({ 'activities.isActive': 1 });

// Virtual for member count
clanSchema.virtual('memberCount').get(function() {
  return this.stats.totalMembers;
});

// Virtual for active activity count
clanSchema.virtual('activeActivityCount').get(function() {
  return this.activities.filter(activity => activity.isActive).length;
});

// Method to add activity
clanSchema.methods.addActivity = function(activityData) {
  this.activities.push(activityData);
  return this.save();
};

// Method to update member stats
clanSchema.methods.updateMemberStats = async function() {
  const User = mongoose.model('User');
  
  const stats = await User.aggregate([
    { $match: { clan: this.name, isActive: true } },
    {
      $group: {
        _id: null,
        totalMembers: { $sum: 1 },
        activeMembers: {
          $sum: {
            $cond: [
              { $gte: ['$lastSeen', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] },
              1,
              0
            ]
          }
        },
        totalPoints: { $sum: '$clanPoints' },
        averageLevel: { $avg: '$level' }
      }
    }
  ]);
  
  if (stats.length > 0) {
    this.stats = {
      totalMembers: stats[0].totalMembers,
      activeMembers: stats[0].activeMembers,
      totalPoints: stats[0].totalPoints,
      averageLevel: Math.round(stats[0].averageLevel * 10) / 10
    };
    return this.save();
  }
  
  return this;
};

// Method to add recent activity
clanSchema.methods.addRecentActivity = function(activityData) {
  this.recentActivity.unshift(activityData);
  
  // Keep only last 50 activities
  if (this.recentActivity.length > 50) {
    this.recentActivity = this.recentActivity.slice(0, 50);
  }
  
  return this.save();
};

// Method to update leaderboards
clanSchema.methods.updateLeaderboards = async function() {
  const User = mongoose.model('User');
  
  // Top Contributors (by clan points)
  const topContributors = await User.find({ clan: this.name, isActive: true })
    .sort({ clanPoints: -1 })
    .limit(10)
    .select('_id clanPoints');
  
  this.leaderboards.topContributors = topContributors.map((user, index) => ({
    user: user._id,
    points: user.clanPoints,
    rank: index + 1
  }));
  
  // Most Active (by recent activity)
  const mostActive = await User.find({ 
    clan: this.name, 
    isActive: true,
    lastSeen: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
  })
    .sort({ lastSeen: -1 })
    .limit(10)
    .select('_id lastSeen');
  
  this.leaderboards.mostActive = mostActive.map((user, index) => ({
    user: user._id,
    activityScore: Math.floor((Date.now() - user.lastSeen) / (1000 * 60 * 60)), // Hours since last seen
    rank: index + 1
  }));
  
  // Top Achievers (by achievement count)
  const topAchievers = await User.find({ clan: this.name, isActive: true })
    .sort({ 'achievements.length': -1 })
    .limit(10)
    .select('_id achievements');
  
  this.leaderboards.achievements = topAchievers.map((user, index) => ({
    user: user._id,
    achievementCount: user.achievements.length,
    rank: index + 1
  }));
  
  return this.save();
};

// Static method to initialize default clans
clanSchema.statics.initializeDefaultClans = async function() {
  const defaultClans = [
    {
      name: 'Naruto',
      description: 'The path of the ninja! Master the way of the shinobi and protect your village.',
      icon: '🍥',
      color: { primary: '#FF6B35', secondary: '#004E89', accent: '#FFD23F' },
      ranks: [
        { name: 'Genin', level: 1, pointsRequired: 0, permissions: ['basic'] },
        { name: 'Chunin', level: 2, pointsRequired: 1000, permissions: ['basic', 'moderate'] },
        { name: 'Jonin', level: 3, pointsRequired: 5000, permissions: ['basic', 'moderate', 'lead'] },
        { name: 'Kage', level: 4, pointsRequired: 15000, permissions: ['basic', 'moderate', 'lead', 'admin'] }
      ]
    },
    {
      name: 'One Piece',
      description: 'Set sail for adventure! Join the crew and search for the ultimate treasure.',
      icon: '🏴‍☠️',
      color: { primary: '#1E3A8A', secondary: '#DC2626', accent: '#F59E0B' },
      ranks: [
        { name: 'Cabin Boy', level: 1, pointsRequired: 0, permissions: ['basic'] },
        { name: 'Pirate', level: 2, pointsRequired: 1000, permissions: ['basic', 'moderate'] },
        { name: 'Captain', level: 3, pointsRequired: 5000, permissions: ['basic', 'moderate', 'lead'] },
        { name: 'Admiral', level: 4, pointsRequired: 15000, permissions: ['basic', 'moderate', 'lead', 'admin'] }
      ]
    },
    {
      name: 'Demon Slayer',
      description: 'Breathe and focus! Master your breathing techniques to slay demons.',
      icon: '⚔️',
      color: { primary: '#7C2D12', secondary: '#1F2937', accent: '#EF4444' },
      ranks: [
        { name: 'Mizunoto', level: 1, pointsRequired: 0, permissions: ['basic'] },
        { name: 'Mizunoe', level: 2, pointsRequired: 1000, permissions: ['basic', 'moderate'] },
        { name: 'Kinoto', level: 3, pointsRequired: 5000, permissions: ['basic', 'moderate', 'lead'] },
        { name: 'Hashira', level: 4, pointsRequired: 15000, permissions: ['basic', 'moderate', 'lead', 'admin'] }
      ]
    },
    {
      name: 'Attack on Titan',
      description: 'Humanity\'s last hope! Join the Survey Corps and fight for freedom.',
      icon: '🗡️',
      color: { primary: '#374151', secondary: '#7C2D12', accent: '#059669' },
      ranks: [
        { name: 'Cadet', level: 1, pointsRequired: 0, permissions: ['basic'] },
        { name: 'Soldier', level: 2, pointsRequired: 1000, permissions: ['basic', 'moderate'] },
        { name: 'Squad Leader', level: 3, pointsRequired: 5000, permissions: ['basic', 'moderate', 'lead'] },
        { name: 'Commander', level: 4, pointsRequired: 15000, permissions: ['basic', 'moderate', 'lead', 'admin'] }
      ]
    },
    {
      name: 'Dragon Ball',
      description: 'Train hard and become the strongest! Gather the Dragon Balls and make your wish.',
      icon: '🐉',
      color: { primary: '#F59E0B', secondary: '#DC2626', accent: '#3B82F6' },
      ranks: [
        { name: 'Earthling', level: 1, pointsRequired: 0, permissions: ['basic'] },
        { name: 'Fighter', level: 2, pointsRequired: 1000, permissions: ['basic', 'moderate'] },
        { name: 'Super Saiyan', level: 3, pointsRequired: 5000, permissions: ['basic', 'moderate', 'lead'] },
        { name: 'God of Destruction', level: 4, pointsRequired: 15000, permissions: ['basic', 'moderate', 'lead', 'admin'] }
      ]
    }
  ];
  
  for (const clanData of defaultClans) {
    const existingClan = await this.findOne({ name: clanData.name });
    if (!existingClan) {
      await this.create(clanData);
      console.log(`✅ Created clan: ${clanData.name}`);
    }
  }
};

export default mongoose.model('Clan', clanSchema);
