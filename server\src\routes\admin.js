import express from 'express';
import User from '../models/User.js';
import Post from '../models/Post.js';
import Event from '../models/Event.js';
import MarketplaceItem from '../models/Marketplace.js';
import Clan from '../models/Clan.js';
import { requireAdmin, requireModerator } from '../middleware/auth.js';
import { catchAsync, AppError } from '../middleware/errorHandler.js';

const router = express.Router();

// All admin routes require admin access
router.use(requireAdmin);

// Get analytics dashboard data
router.get('/analytics', catchAsync(async (req, res) => {
  const [
    totalUsers,
    activeUsers,
    newUsersToday,
    totalPosts,
    totalEvents,
    totalMarketplaceItems,
    clanDistribution
  ] = await Promise.all([
    User.countDocuments({ isActive: true }),
    User.countDocuments({ 
      isActive: true, 
      lastSeen: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
    }),
    User.countDocuments({ 
      isActive: true,
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    }),
    Post.countDocuments({ isActive: true }),
    Event.countDocuments({ status: 'published' }),
    MarketplaceItem.countDocuments({ isActive: true }),
    User.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$clan', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])
  ]);

  // Calculate percentages for clan distribution
  const clanDistributionWithPercentage = clanDistribution.map(clan => ({
    clan: clan._id,
    count: clan.count,
    percentage: Math.round((clan.count / totalUsers) * 100 * 10) / 10
  }));

  const analytics = {
    totalUsers,
    activeUsers,
    newUsersToday,
    totalPosts,
    totalEvents,
    totalMarketplaceItems,
    clanDistribution: clanDistributionWithPercentage,
    engagementMetrics: {
      dailyActiveUsers: activeUsers,
      averageSessionTime: '24 minutes', // Mock data
      postsPerDay: Math.floor(totalPosts / 30), // Mock calculation
      messagesPerDay: 8765, // Mock data
      eventsPerWeek: Math.floor(totalEvents / 4) // Mock calculation
    }
  };

  res.json({
    success: true,
    analytics
  });
}));

// Get reported content
router.get('/reports', catchAsync(async (req, res) => {
  const { type = 'posts', status = 'pending' } = req.query;
  
  let reports = [];
  
  if (type === 'posts') {
    reports = await Post.find({ 
      isReported: true,
      'reports.status': status
    })
    .populate('author', 'username avatar clan')
    .populate('reports.reporter', 'username avatar')
    .sort({ 'reports.reportedAt': -1 });
  }
  
  res.json({
    success: true,
    reports
  });
}));

// Moderate content
router.patch('/moderate/:contentType/:contentId', catchAsync(async (req, res) => {
  const { contentType, contentId } = req.params;
  const { action, reason } = req.body; // action: 'approve', 'remove', 'warn'
  
  if (!['approve', 'remove', 'warn'].includes(action)) {
    throw new AppError('Invalid moderation action', 400);
  }
  
  let content;
  
  if (contentType === 'post') {
    content = await Post.findById(contentId);
    if (action === 'remove') {
      content.isActive = false;
    }
  }
  
  if (!content) {
    throw new AppError('Content not found', 404);
  }
  
  // Update report status
  content.reports.forEach(report => {
    if (report.status === 'pending') {
      report.status = 'reviewed';
    }
  });
  
  await content.save();
  
  res.json({
    success: true,
    message: `Content ${action}d successfully`
  });
}));

// Get user management data
router.get('/users', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 50, 
    role, 
    clan, 
    isActive = true,
    search 
  } = req.query;
  
  const query = {};
  if (role) query.role = role;
  if (clan) query.clan = clan;
  if (isActive !== undefined) query.isActive = isActive === 'true';
  if (search) {
    query.$or = [
      { username: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } }
    ];
  }
  
  const users = await User.find(query)
    .select('-password')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));
  
  const total = await User.countDocuments(query);
  
  res.json({
    success: true,
    users,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Update user status
router.patch('/users/:userId/status', catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { isActive, reason } = req.body;
  
  const user = await User.findByIdAndUpdate(
    userId,
    { 
      isActive,
      ...(reason && { moderationReason: reason })
    },
    { new: true }
  ).select('-password');
  
  if (!user) {
    throw new AppError('User not found', 404);
  }
  
  res.json({
    success: true,
    message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
    user: user.toSafeObject()
  });
}));

// System actions
router.post('/system/announcement', catchAsync(async (req, res) => {
  const { title, message, type = 'info' } = req.body;
  
  if (!title || !message) {
    throw new AppError('Title and message are required', 400);
  }
  
  // In a real implementation, this would send notifications to all users
  console.log('System announcement:', { title, message, type });
  
  res.json({
    success: true,
    message: 'Announcement sent successfully'
  });
}));

router.post('/system/maintenance', catchAsync(async (req, res) => {
  const { enabled, message } = req.body;
  
  // In a real implementation, this would toggle maintenance mode
  console.log('Maintenance mode:', { enabled, message });
  
  res.json({
    success: true,
    message: `Maintenance mode ${enabled ? 'enabled' : 'disabled'}`
  });
}));

router.post('/system/backup', catchAsync(async (req, res) => {
  // In a real implementation, this would trigger a database backup
  console.log('Database backup triggered');
  
  res.json({
    success: true,
    message: 'Database backup initiated'
  });
}));

// Get system status
router.get('/system/status', catchAsync(async (req, res) => {
  const status = {
    server: 'online',
    database: 'healthy',
    apiResponseTime: '45ms',
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    timestamp: new Date().toISOString()
  };
  
  res.json({
    success: true,
    status
  });
}));

export default router;
