import winston from 'winston';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create logger factory
export const createLogger = (serviceName = 'service') => {
  // Define log levels
  const levels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
  };

  // Define colors for each level
  const colors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'white',
  };

  // Tell winston that you want to link the colors
  winston.addColors(colors);

  // Define which level to log based on environment
  const level = () => {
    const env = process.env.NODE_ENV || 'development';
    const isDevelopment = env === 'development';
    return isDevelopment ? 'debug' : 'warn';
  };

  // Define format for logs
  const format = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
    winston.format.colorize({ all: true }),
    winston.format.printf(
      (info) => `${info.timestamp} [${serviceName.toUpperCase()}] ${info.level}: ${info.message}`,
    ),
  );

  // Create logs directory if it doesn't exist
  const logsDir = path.join(__dirname, `../../logs/${serviceName}`);
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  // Define which transports the logger must use
  const transports = [
    // Console transport
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    
    // File transport for errors
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    
    // File transport for all logs
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
  ];

  // Create the logger
  const logger = winston.createLogger({
    level: level(),
    levels,
    format,
    transports,
    exceptionHandlers: [
      new winston.transports.File({ 
        filename: path.join(logsDir, 'exceptions.log') 
      })
    ],
    rejectionHandlers: [
      new winston.transports.File({ 
        filename: path.join(logsDir, 'rejections.log') 
      })
    ],
    exitOnError: false,
  });

  return logger;
};

// Default logger
const logger = createLogger('default');

export default logger;
