{"name": "animeverse-microservices", "version": "1.0.0", "description": "AnimeVerse Microservices Architecture - Complete anime social platform", "main": "index.js", "type": "module", "scripts": {"install:all": "npm run install:gateway && npm run install:services && npm run install:client", "install:gateway": "cd services/API-Gateway && npm install", "install:services": "cd User-Service && npm install && cd ../services/Social-Service && npm install && cd ../Event-Service && npm install && cd ../Marketplace-Service && npm install && cd ../Chat-Service && npm install && cd ../Anime-Service && npm install", "install:client": "cd client && npm install", "dev": "concurrently \"npm run dev:gateway\" \"npm run dev:user\" \"npm run dev:social\" \"npm run dev:event\" \"npm run dev:marketplace\" \"npm run dev:chat\" \"npm run dev:anime\" \"npm run dev:client\"", "dev:gateway": "cd services/API-Gateway && npm run dev", "dev:user": "cd User-Service && npm run dev", "dev:social": "cd services/Social-Service && npm run dev", "dev:event": "cd services/Event-Service && npm run dev", "dev:marketplace": "cd services/Marketplace-Service && npm run dev", "dev:chat": "cd services/Chat-Service && npm run dev", "dev:anime": "cd services/Anime-Service && npm run dev", "dev:client": "cd client && npm run dev", "start": "concurrently \"npm run start:gateway\" \"npm run start:user\" \"npm run start:social\" \"npm run start:event\" \"npm run start:marketplace\" \"npm run start:chat\" \"npm run start:anime\"", "start:gateway": "cd services/API-Gateway && npm start", "start:user": "cd User-Service && npm start", "start:social": "cd services/Social-Service && npm start", "start:event": "cd services/Event-Service && npm start", "start:marketplace": "cd services/Marketplace-Service && npm start", "start:chat": "cd services/Chat-Service && npm start", "start:anime": "cd services/Anime-Service && npm start", "test": "npm run test:user && npm run test:social && npm run test:event && npm run test:marketplace && npm run test:chat && npm run test:anime", "test:user": "cd User-Service && npm test", "test:social": "cd services/Social-Service && npm test", "test:event": "cd services/Event-Service && npm test", "test:marketplace": "cd services/Marketplace-Service && npm test", "test:chat": "cd services/Chat-Service && npm test", "test:anime": "cd services/Anime-Service && npm test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --rmi all", "init:db": "cd User-Service && npm run init-db", "health": "curl -s http://localhost:3000/health | jq", "health:services": "curl -s http://localhost:3000/health/services | jq"}, "keywords": ["anime", "social-platform", "microservices", "nodejs", "mongodb", "react"], "author": "AnimeVerse Team", "license": "MIT", "devDependencies": {"concurrently": "^9.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}