import express from 'express';
import { catchAsync, AppError } from '../middleware/errorHandler.js';

const router = express.Router();

// Mock anime data (in production, this would come from an external API like Jikan)
const mockAnimeData = [
  {
    id: '1',
    title: 'One Piece',
    episodes: 1100,
    status: 'ongoing',
    genre: ['Action', 'Adventure', 'Comedy'],
    studio: 'Toei Animation',
    year: 1999,
    rating: 9.0,
    image: 'onepiece.jpg',
    description: 'Follow Monkey D<PERSON> and his crew as they search for the ultimate treasure.'
  },
  {
    id: '2',
    title: '<PERSON><PERSON><PERSON>',
    episodes: 720,
    status: 'completed',
    genre: ['Action', 'Adventure', 'Martial Arts'],
    studio: 'Pierrot',
    year: 2002,
    rating: 8.7,
    image: 'naruto.jpg',
    description: 'A young ninja seeks recognition and dreams of becoming the Hokage.'
  },
  {
    id: '3',
    title: 'Demon Slayer',
    episodes: 44,
    status: 'ongoing',
    genre: ['Action', 'Supernatural', 'Historical'],
    studio: 'Ufotable',
    year: 2019,
    rating: 8.9,
    image: 'demonslayer.jpg',
    description: 'A young boy becomes a demon slayer to save his sister.'
  }
];

// Search anime
router.get('/search', catchAsync(async (req, res) => {
  const { q, genre, year, studio, status } = req.query;
  
  let results = [...mockAnimeData];
  
  if (q) {
    results = results.filter(anime => 
      anime.title.toLowerCase().includes(q.toLowerCase())
    );
  }
  
  if (genre) {
    results = results.filter(anime => 
      anime.genre.some(g => g.toLowerCase().includes(genre.toLowerCase()))
    );
  }
  
  if (year) {
    results = results.filter(anime => anime.year.toString() === year);
  }
  
  if (studio) {
    results = results.filter(anime => 
      anime.studio.toLowerCase().includes(studio.toLowerCase())
    );
  }
  
  if (status) {
    results = results.filter(anime => anime.status === status);
  }
  
  res.json({
    success: true,
    anime: results,
    total: results.length
  });
}));

// Get trending anime
router.get('/trending', catchAsync(async (req, res) => {
  // Mock trending data
  const trending = mockAnimeData
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 10);
  
  res.json({
    success: true,
    anime: trending
  });
}));

// Get anime by ID
router.get('/:animeId', catchAsync(async (req, res) => {
  const { animeId } = req.params;
  
  const anime = mockAnimeData.find(a => a.id === animeId);
  
  if (!anime) {
    throw new AppError('Anime not found', 404, 'ANIME_NOT_FOUND');
  }
  
  res.json({
    success: true,
    anime
  });
}));

// Get recommendations
router.get('/recommendations/:userId', catchAsync(async (req, res) => {
  // Mock recommendations based on user's clan and watching history
  const recommendations = mockAnimeData.slice(0, 5);
  
  res.json({
    success: true,
    recommendations
  });
}));

export default router;
