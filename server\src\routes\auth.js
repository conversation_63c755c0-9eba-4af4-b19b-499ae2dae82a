import express from 'express';
import bcrypt from 'bcryptjs';
import { body, validationResult } from 'express-validator';
import User from '../models/User.js';
import Clan from '../models/Clan.js';
import { generateToken } from '../middleware/auth.js';
import { catchAsync, AppError } from '../middleware/errorHandler.js';

const router = express.Router();

// Validation middleware
const validateRegistration = [
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('clan')
    .isIn(['Naruto', 'One Piece', 'Demon Slayer', 'Attack on Titan', 'Dragon Ball'])
    .withMessage('Please select a valid clan')
];

const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Register new user
router.post('/register', validateRegistration, catchAsync(async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { username, email, password, clan, bio = '' } = req.body;
  
  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { username }]
  });
  
  if (existingUser) {
    if (existingUser.email === email) {
      throw new AppError('Email already registered', 400, 'EMAIL_EXISTS');
    }
    if (existingUser.username === username) {
      throw new AppError('Username already taken', 400, 'USERNAME_EXISTS');
    }
  }
  
  // Create new user
  const user = new User({
    username,
    email,
    password, // Will be hashed by pre-save middleware
    clan,
    bio
  });
  
  await user.save();
  
  // Generate JWT token
  const token = generateToken(user._id);
  
  // Update clan member count
  await Clan.findOneAndUpdate(
    { name: clan },
    { $inc: { 'stats.totalMembers': 1 } }
  );
  
  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token,
    user: user.toSafeObject()
  });
}));

// Login user
router.post('/login', validateLogin, catchAsync(async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { email, password } = req.body;
  
  // Find user by email
  const user = await User.findOne({ email }).select('+password');
  
  if (!user) {
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
  }
  
  if (!user.isActive) {
    throw new AppError('Account has been deactivated', 401, 'ACCOUNT_DEACTIVATED');
  }
  
  // Check password
  const isPasswordValid = await user.comparePassword(password);
  
  if (!isPasswordValid) {
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
  }
  
  // Update last seen
  user.lastSeen = new Date();
  user.isOnline = true;
  await user.save();
  
  // Generate JWT token
  const token = generateToken(user._id);
  
  res.json({
    success: true,
    message: 'Login successful',
    token,
    user: user.toSafeObject()
  });
}));

// Logout user
router.post('/logout', catchAsync(async (req, res) => {
  // In a stateless JWT system, logout is handled client-side
  // But we can update user's online status if they provide a token
  
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  
  if (token) {
    try {
      const jwt = await import('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'animeverse_secret_key');
      
      await User.findByIdAndUpdate(decoded.userId, {
        isOnline: false,
        lastSeen: new Date()
      });
    } catch (error) {
      // Ignore token errors during logout
    }
  }
  
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
}));

// Check if username is available
router.get('/check-username/:username', catchAsync(async (req, res) => {
  const { username } = req.params;
  
  if (!username || username.length < 3) {
    throw new AppError('Username must be at least 3 characters long', 400);
  }
  
  const existingUser = await User.findOne({ username });
  
  res.json({
    success: true,
    available: !existingUser,
    message: existingUser ? 'Username is already taken' : 'Username is available'
  });
}));

// Check if email is available
router.get('/check-email/:email', catchAsync(async (req, res) => {
  const { email } = req.params;
  
  if (!email || !email.includes('@')) {
    throw new AppError('Please provide a valid email address', 400);
  }
  
  const existingUser = await User.findOne({ email: email.toLowerCase() });
  
  res.json({
    success: true,
    available: !existingUser,
    message: existingUser ? 'Email is already registered' : 'Email is available'
  });
}));

// Get available clans
router.get('/clans', catchAsync(async (req, res) => {
  const clans = await Clan.find({}, 'name description icon color stats.totalMembers')
    .sort({ 'stats.totalMembers': -1 });
  
  res.json({
    success: true,
    clans
  });
}));

// Forgot password (placeholder for future implementation)
router.post('/forgot-password', catchAsync(async (req, res) => {
  const { email } = req.body;
  
  if (!email) {
    throw new AppError('Email is required', 400);
  }
  
  const user = await User.findOne({ email });
  
  if (!user) {
    // Don't reveal if email exists or not for security
    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent'
    });
    return;
  }
  
  // TODO: Implement password reset functionality
  // 1. Generate reset token
  // 2. Save token to user record with expiry
  // 3. Send email with reset link
  
  res.json({
    success: true,
    message: 'Password reset functionality will be implemented soon'
  });
}));

// Reset password (placeholder for future implementation)
router.post('/reset-password', catchAsync(async (req, res) => {
  const { token, newPassword } = req.body;
  
  if (!token || !newPassword) {
    throw new AppError('Token and new password are required', 400);
  }
  
  // TODO: Implement password reset functionality
  // 1. Verify reset token
  // 2. Check if token is not expired
  // 3. Update user password
  // 4. Invalidate reset token
  
  res.json({
    success: true,
    message: 'Password reset functionality will be implemented soon'
  });
}));

// Refresh token (placeholder for future implementation)
router.post('/refresh-token', catchAsync(async (req, res) => {
  const { refreshToken } = req.body;
  
  if (!refreshToken) {
    throw new AppError('Refresh token is required', 400);
  }
  
  // TODO: Implement refresh token functionality
  // 1. Verify refresh token
  // 2. Generate new access token
  // 3. Optionally generate new refresh token
  
  res.json({
    success: true,
    message: 'Refresh token functionality will be implemented soon'
  });
}));

// Verify email (placeholder for future implementation)
router.get('/verify-email/:token', catchAsync(async (req, res) => {
  const { token } = req.params;
  
  if (!token) {
    throw new AppError('Verification token is required', 400);
  }
  
  // TODO: Implement email verification
  // 1. Verify token
  // 2. Mark user as verified
  // 3. Update user record
  
  res.json({
    success: true,
    message: 'Email verification functionality will be implemented soon'
  });
}));

export default router;
