import mongoose from 'mongoose';

const marketplaceItemSchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  
  // Seller Information
  seller: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Category and Type
  category: {
    type: String,
    enum: ['figures', 'manga', 'posters', 'accessories', 'cards', 'clothing', 'collectibles', 'other'],
    required: true
  },
  
  subcategory: String,
  
  // Anime Reference
  animeReference: {
    animeId: String,
    title: String,
    character: String,
    series: String
  },
  
  // Pricing
  price: {
    current: {
      type: Number,
      required: true,
      min: 0
    },
    original: Number,
    currency: {
      type: String,
      default: 'USD'
    }
  },
  
  // Price History for tracking
  priceHistory: [{
    price: Number,
    date: {
      type: Date,
      default: Date.now
    },
    reason: String // 'price_drop', 'price_increase', 'sale', 'promotion'
  }],
  
  // Condition and Details
  condition: {
    type: String,
    enum: ['new', 'like-new', 'good', 'fair', 'poor'],
    required: true
  },
  
  conditionDetails: String,
  
  // Availability
  availability: {
    status: {
      type: String,
      enum: ['available', 'sold', 'reserved', 'pending', 'removed'],
      default: 'available'
    },
    quantity: {
      type: Number,
      default: 1,
      min: 0
    }
  },
  
  // Images
  images: [{
    url: {
      type: String,
      required: true
    },
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    }
  }],
  
  // Shipping and Location
  shipping: {
    methods: [{
      name: String, // 'standard', 'express', 'overnight'
      price: Number,
      estimatedDays: String
    }],
    freeShipping: {
      type: Boolean,
      default: false
    },
    freeShippingThreshold: Number,
    shipsFrom: {
      city: String,
      country: String,
      region: String
    },
    shipsTo: [String], // Array of countries/regions
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      weight: Number,
      unit: {
        type: String,
        enum: ['cm', 'in'],
        default: 'cm'
      }
    }
  },
  
  // Tags and Search
  tags: [String],
  
  // Engagement
  views: {
    type: Number,
    default: 0
  },
  
  viewedBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    viewedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  likes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    likedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Wishlist tracking
  inWishlists: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Questions and Answers
  questions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    question: {
      type: String,
      required: true,
      maxlength: 500
    },
    answer: {
      type: String,
      maxlength: 1000
    },
    answeredAt: Date,
    isPublic: {
      type: Boolean,
      default: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Trading
  trading: {
    acceptsTrades: {
      type: Boolean,
      default: false
    },
    tradePreferences: [String], // What they're looking for
    tradeOffers: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      offeredItems: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'MarketplaceItem'
      }],
      message: String,
      status: {
        type: String,
        enum: ['pending', 'accepted', 'declined', 'countered'],
        default: 'pending'
      },
      createdAt: {
        type: Date,
        default: Date.now
      },
      respondedAt: Date
    }]
  },
  
  // Purchase History
  purchases: [{
    buyer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    quantity: Number,
    totalPrice: Number,
    shippingMethod: String,
    shippingAddress: {
      name: String,
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String
    },
    status: {
      type: String,
      enum: ['pending', 'paid', 'shipped', 'delivered', 'cancelled', 'refunded'],
      default: 'pending'
    },
    trackingNumber: String,
    purchasedAt: {
      type: Date,
      default: Date.now
    },
    shippedAt: Date,
    deliveredAt: Date
  }],
  
  // Seller Ratings for this item
  ratings: [{
    buyer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      required: true
    },
    review: String,
    aspects: {
      itemCondition: { type: Number, min: 1, max: 5 },
      shipping: { type: Number, min: 1, max: 5 },
      communication: { type: Number, min: 1, max: 5 }
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Status and Moderation
  isActive: {
    type: Boolean,
    default: true
  },
  
  isFeatured: {
    type: Boolean,
    default: false
  },
  
  isReported: {
    type: Boolean,
    default: false
  },
  
  reports: [{
    reporter: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: {
      type: String,
      enum: ['counterfeit', 'inappropriate', 'spam', 'overpriced', 'misleading', 'other']
    },
    description: String,
    reportedAt: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['pending', 'reviewed', 'resolved', 'dismissed'],
      default: 'pending'
    }
  }],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  
  // Auto-expiry
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // 90 days
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
marketplaceItemSchema.index({ seller: 1 });
marketplaceItemSchema.index({ category: 1 });
marketplaceItemSchema.index({ 'availability.status': 1 });
marketplaceItemSchema.index({ 'price.current': 1 });
marketplaceItemSchema.index({ createdAt: -1 });
marketplaceItemSchema.index({ tags: 1 });
marketplaceItemSchema.index({ 'animeReference.animeId': 1 });
marketplaceItemSchema.index({ isActive: 1 });

// Compound indexes
marketplaceItemSchema.index({ category: 1, 'availability.status': 1, 'price.current': 1 });
marketplaceItemSchema.index({ isActive: 1, 'availability.status': 1, createdAt: -1 });

// TTL index for auto-expiry
marketplaceItemSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Virtual for like count
marketplaceItemSchema.virtual('likeCount').get(function() {
  return this.likes.length;
});

// Virtual for wishlist count
marketplaceItemSchema.virtual('wishlistCount').get(function() {
  return this.inWishlists.length;
});

// Virtual for average rating
marketplaceItemSchema.virtual('averageRating').get(function() {
  if (this.ratings.length === 0) return 0;
  const sum = this.ratings.reduce((acc, r) => acc + r.rating, 0);
  return Math.round((sum / this.ratings.length) * 10) / 10;
});

// Virtual for discount percentage
marketplaceItemSchema.virtual('discountPercentage').get(function() {
  if (!this.price.original || this.price.original <= this.price.current) return 0;
  return Math.round(((this.price.original - this.price.current) / this.price.original) * 100);
});

// Virtual for primary image
marketplaceItemSchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary || this.images[0] || null;
});

// Method to check if user liked the item
marketplaceItemSchema.methods.isLikedBy = function(userId) {
  return this.likes.some(like => like.user.toString() === userId.toString());
};

// Method to toggle like
marketplaceItemSchema.methods.toggleLike = function(userId) {
  const likeIndex = this.likes.findIndex(like => like.user.toString() === userId.toString());
  
  if (likeIndex > -1) {
    this.likes.splice(likeIndex, 1);
  } else {
    this.likes.push({ user: userId });
  }
  
  return this.save();
};

// Method to add to wishlist
marketplaceItemSchema.methods.addToWishlist = function(userId) {
  const isInWishlist = this.inWishlists.some(w => w.user.toString() === userId.toString());
  
  if (!isInWishlist) {
    this.inWishlists.push({ user: userId });
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Method to remove from wishlist
marketplaceItemSchema.methods.removeFromWishlist = function(userId) {
  this.inWishlists = this.inWishlists.filter(w => w.user.toString() !== userId.toString());
  return this.save();
};

// Method to increment views
marketplaceItemSchema.methods.incrementViews = function(userId) {
  // Only count unique views
  const hasViewed = this.viewedBy.some(view => view.user.toString() === userId.toString());
  if (!hasViewed) {
    this.views += 1;
    this.viewedBy.push({ user: userId });
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to update price with history
marketplaceItemSchema.methods.updatePrice = function(newPrice, reason = 'price_change') {
  // Add to price history
  this.priceHistory.push({
    price: this.price.current,
    reason: reason
  });
  
  // Update current price
  this.price.current = newPrice;
  
  return this.save();
};

// Static method to get trending items
marketplaceItemSchema.statics.getTrending = function(limit = 10) {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        isActive: true,
        'availability.status': 'available',
        createdAt: { $gte: oneDayAgo }
      }
    },
    {
      $addFields: {
        engagementScore: {
          $add: [
            '$views',
            { $multiply: [{ $size: '$likes' }, 2] },
            { $multiply: [{ $size: '$inWishlists' }, 3] }
          ]
        }
      }
    },
    {
      $sort: { engagementScore: -1, createdAt: -1 }
    },
    {
      $limit: limit
    }
  ]);
};

// Static method to get items by category
marketplaceItemSchema.statics.getByCategory = function(category, limit = 20) {
  return this.find({
    category: category,
    isActive: true,
    'availability.status': 'available'
  })
  .sort({ createdAt: -1 })
  .limit(limit)
  .populate('seller', 'username avatar clan');
};

// Pre-save middleware to update timestamps
marketplaceItemSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

export default mongoose.model('MarketplaceItem', marketplaceItemSchema);
