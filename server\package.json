{"name": "animeverse-server", "version": "1.0.0", "description": "AnimeVerse backend server - Social platform for anime fans", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "dev:full": "concurrently \"npm run dev\" \"cd ../client && npm run dev\"", "init-db": "node src/scripts/initDatabase.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["anime", "social", "express", "nodejs", "postgresql"], "author": "AnimeVerse Team", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.8", "socket.io": "^4.7.4", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}