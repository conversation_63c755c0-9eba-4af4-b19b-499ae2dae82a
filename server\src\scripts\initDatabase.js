import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/User.js';
import Clan from '../models/Clan.js';
import Post from '../models/Post.js';
import Event from '../models/Event.js';
import MarketplaceItem from '../models/Marketplace.js';

dotenv.config();

const initDatabase = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/animeverse');
    console.log('🍃 Connected to MongoDB');

    // Initialize default clans
    await Clan.initializeDefaultClans();
    console.log('✅ Default clans initialized');

    // Create admin user if not exists
    const adminExists = await User.findOne({ role: 'admin' });
    if (!adminExists) {
      const adminUser = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin123!',
        clan: '<PERSON><PERSON><PERSON>',
        role: 'admin',
        bio: 'AnimeVerse Administrator',
        level: 100,
        experience: 0,
        clanPoints: 10000,
        isVerified: true
      });

      await adminUser.save();
      console.log('✅ Admin user created');
    }

    // Create sample users
    const sampleUsers = [
      {
        username: 'AnimeKing_2024',
        email: '<EMAIL>',
        password: 'Password123!',
        clan: 'Naruto',
        bio: 'Passionate about all things anime! Currently watching One Piece.',
        level: 15,
        experience: 750,
        clanPoints: 2500,
        avatar: '👑'
      },
      {
        username: 'MangaCollector_Pro',
        email: '<EMAIL>',
        password: 'Password123!',
        clan: 'One Piece',
        bio: 'Manga collector and reviewer. Always looking for rare finds!',
        level: 22,
        experience: 1200,
        clanPoints: 3800,
        avatar: '📚'
      },
      {
        username: 'DragonBallFan',
        email: '<EMAIL>',
        password: 'Password123!',
        clan: 'Dragon Ball',
        bio: 'Over 9000! Dragon Ball is life. Goku is the GOAT.',
        level: 18,
        experience: 900,
        clanPoints: 2100,
        avatar: '🐉'
      },
      {
        username: 'DemonSlayerSenpai',
        email: '<EMAIL>',
        password: 'Password123!',
        clan: 'Demon Slayer',
        bio: 'Breathing techniques master. Tanjiro is my inspiration.',
        level: 20,
        experience: 1500,
        clanPoints: 3200,
        avatar: '⚔️'
      },
      {
        username: 'TitanHunter',
        email: '<EMAIL>',
        password: 'Password123!',
        clan: 'Attack on Titan',
        bio: 'Dedicate your heart! Fighting for humanity\'s freedom.',
        level: 25,
        experience: 2000,
        clanPoints: 4500,
        avatar: '🗡️'
      }
    ];

    for (const userData of sampleUsers) {
      const existingUser = await User.findOne({ username: userData.username });
      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        console.log(`✅ Created user: ${userData.username}`);
      }
    }

    // Create sample posts
    const users = await User.find({ role: 'user' }).limit(5);
    
    const samplePosts = [
      {
        author: users[0]._id,
        content: 'Just finished watching the latest One Piece episode! The animation quality is incredible! 🔥 What did everyone think about that epic fight scene?',
        type: 'discussion',
        tags: ['one-piece', 'anime', 'discussion'],
        animeReference: {
          title: 'One Piece',
          episode: 1100
        }
      },
      {
        author: users[1]._id,
        content: 'My manga collection is finally complete! Just got the last volume of Demon Slayer. The artwork is absolutely stunning! 📚✨',
        type: 'image',
        tags: ['manga', 'collection', 'demon-slayer'],
        images: [{ url: 'manga-collection.jpg', caption: 'My complete Demon Slayer collection' }]
      },
      {
        author: users[2]._id,
        content: 'Dragon Ball Super: Super Hero was amazing! The animation style was so unique and refreshing. Gohan finally got the spotlight he deserved! 🐉',
        type: 'review',
        tags: ['dragon-ball', 'movie', 'review'],
        review: {
          animeTitle: 'Dragon Ball Super: Super Hero',
          rating: 9,
          recommendation: 'highly_recommended'
        }
      },
      {
        author: users[3]._id,
        content: 'Working on some Tanjiro fan art! The breathing techniques are so visually stunning, I had to capture them in my drawing. What\'s your favorite breathing style?',
        type: 'fanart',
        tags: ['fanart', 'demon-slayer', 'tanjiro'],
        images: [{ url: 'tanjiro-fanart.jpg', caption: 'Water Breathing First Form' }]
      },
      {
        author: users[4]._id,
        content: 'Attack on Titan finale still gives me chills! The way everything came together was perfect. Eren\'s character development throughout the series was incredible. 😭',
        type: 'discussion',
        tags: ['attack-on-titan', 'finale', 'discussion'],
        animeReference: {
          title: 'Attack on Titan',
          character: 'Eren Yeager'
        }
      }
    ];

    for (const postData of samplePosts) {
      const existingPost = await Post.findOne({ 
        author: postData.author, 
        content: postData.content 
      });
      
      if (!existingPost) {
        const post = new Post(postData);
        await post.save();
        console.log(`✅ Created post by ${users.find(u => u._id.equals(postData.author)).username}`);
      }
    }

    // Create sample events
    const sampleEvents = [
      {
        title: 'Anime Expo 2024',
        description: 'The biggest anime convention of the year! Join thousands of anime fans for panels, screenings, cosplay contests, and more!',
        type: 'convention',
        organizer: users[0]._id,
        startDate: new Date('2024-07-04T09:00:00Z'),
        endDate: new Date('2024-07-07T18:00:00Z'),
        location: {
          type: 'physical',
          venue: 'Los Angeles Convention Center',
          city: 'Los Angeles',
          country: 'USA'
        },
        capacity: { max: 5000 },
        pricing: { isFree: false, price: 75 },
        tags: ['convention', 'cosplay', 'panels'],
        status: 'published'
      },
      {
        title: 'One Piece Episode 1100 Watch Party',
        description: 'Join us for the milestone 1100th episode of One Piece! We\'ll watch together and discuss all the epic moments!',
        type: 'watch-party',
        organizer: users[1]._id,
        startDate: new Date('2024-02-15T20:00:00Z'),
        endDate: new Date('2024-02-15T21:30:00Z'),
        location: {
          type: 'online',
          venue: 'Discord Voice Channel'
        },
        capacity: { max: 100 },
        pricing: { isFree: true },
        animeReference: {
          title: 'One Piece',
          episode: 1100
        },
        watchParty: {
          streamingPlatform: 'Crunchyroll',
          episodes: [1100],
          syncEnabled: true
        },
        clan: 'One Piece',
        tags: ['watch-party', 'one-piece', 'milestone'],
        status: 'published'
      }
    ];

    for (const eventData of sampleEvents) {
      const existingEvent = await Event.findOne({ title: eventData.title });
      if (!existingEvent) {
        const event = new Event(eventData);
        await event.save();
        console.log(`✅ Created event: ${eventData.title}`);
      }
    }

    // Create sample marketplace items
    const sampleItems = [
      {
        title: 'Demon Slayer Tanjiro Kamado Figure',
        description: 'High-quality PVC figure of Tanjiro Kamado in his demon slayer uniform. Perfect for any Demon Slayer fan!',
        seller: users[3]._id,
        category: 'figures',
        animeReference: {
          title: 'Demon Slayer',
          character: 'Tanjiro Kamado'
        },
        price: { current: 89.99, original: 120.00 },
        condition: 'new',
        images: [
          { url: 'tanjiro-figure-1.jpg', isPrimary: true },
          { url: 'tanjiro-figure-2.jpg' }
        ],
        shipping: {
          methods: [
            { name: 'standard', price: 5.99, estimatedDays: '5-7' },
            { name: 'express', price: 12.99, estimatedDays: '2-3' }
          ],
          shipsFrom: { city: 'Tokyo', country: 'Japan' }
        },
        tags: ['demon-slayer', 'tanjiro', 'figure', 'collectible']
      },
      {
        title: 'One Piece Manga Volume 1-100 Complete Set',
        description: 'Complete collection of One Piece manga volumes 1-100 in excellent condition. A must-have for any One Piece fan!',
        seller: users[1]._id,
        category: 'manga',
        animeReference: {
          title: 'One Piece',
          series: 'Manga'
        },
        price: { current: 899.99 },
        condition: 'like-new',
        images: [
          { url: 'onepiece-manga-set.jpg', isPrimary: true }
        ],
        shipping: {
          methods: [
            { name: 'standard', price: 15.99, estimatedDays: '7-10' }
          ],
          shipsFrom: { city: 'New York', country: 'USA' }
        },
        tags: ['one-piece', 'manga', 'complete-set', 'collection']
      }
    ];

    for (const itemData of sampleItems) {
      const existingItem = await MarketplaceItem.findOne({ title: itemData.title });
      if (!existingItem) {
        const item = new MarketplaceItem(itemData);
        await item.save();
        console.log(`✅ Created marketplace item: ${itemData.title}`);
      }
    }

    console.log('🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run initialization if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initDatabase();
}

export default initDatabase;
