import express from 'express';
import Chat from '../models/Chat.js';
import User from '../models/User.js';
import { catchAsync, AppError, notFoundError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get user's chats
router.get('/', catchAsync(async (req, res) => {
  const { limit = 50 } = req.query;
  
  const chats = await Chat.getUserChats(req.user._id, parseInt(limit));
  
  // Add unread count for each chat
  const chatsWithUnread = chats.map(chat => {
    const chatObj = chat.toObject();
    chatObj.unreadCount = chat.getUnreadCount(req.user._id);
    return chatObj;
  });
  
  res.json({
    success: true,
    chats: chatsWithUnread
  });
}));

// Create or get direct chat
router.post('/direct', catchAsync(async (req, res) => {
  const { userId } = req.body;
  
  if (!userId) {
    throw new AppError('User ID is required', 400);
  }
  
  if (userId === req.user._id.toString()) {
    throw new AppError('Cannot create chat with yourself', 400);
  }
  
  const otherUser = await User.findById(userId);
  if (!otherUser || !otherUser.isActive) {
    throw notFoundError('User');
  }
  
  // Check if direct chat already exists
  let chat = await Chat.findDirectChat(req.user._id, userId);
  
  if (!chat) {
    // Create new direct chat
    chat = new Chat({
      type: 'direct',
      participants: [
        { user: req.user._id, role: 'member' },
        { user: userId, role: 'member' }
      ]
    });
    await chat.save();
  }
  
  await chat.populate('participants.user', 'username avatar clan isOnline lastSeen');
  
  res.json({
    success: true,
    chat
  });
}));

// Get chat messages
router.get('/:chatId/messages', catchAsync(async (req, res) => {
  const { chatId } = req.params;
  const { page = 1, limit = 50 } = req.query;
  
  const chat = await Chat.findById(chatId)
    .populate('messages.sender', 'username avatar clan');
  
  if (!chat || !chat.isParticipant(req.user._id)) {
    throw new AppError('Chat not found or access denied', 404);
  }
  
  const startIndex = Math.max(0, chat.messages.length - (page * limit));
  const endIndex = chat.messages.length - ((page - 1) * limit);
  
  const messages = chat.messages
    .slice(startIndex, endIndex)
    .filter(msg => !msg.isDeleted)
    .reverse();
  
  res.json({
    success: true,
    messages,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: chat.messages.filter(msg => !msg.isDeleted).length,
      hasMore: startIndex > 0
    }
  });
}));

// Mark messages as read
router.post('/:chatId/read', catchAsync(async (req, res) => {
  const { chatId } = req.params;
  const { messageId } = req.body;
  
  const chat = await Chat.findById(chatId);
  
  if (!chat || !chat.isParticipant(req.user._id)) {
    throw new AppError('Chat not found or access denied', 404);
  }
  
  await chat.markAsRead(req.user._id, messageId);
  
  res.json({
    success: true,
    message: 'Messages marked as read'
  });
}));

// Get clan chats
router.get('/clan/:clanName', catchAsync(async (req, res) => {
  const { clanName } = req.params;
  
  if (req.user.clan !== clanName && req.user.role !== 'admin') {
    throw new AppError('Access denied to clan chats', 403);
  }
  
  const chats = await Chat.getClanChats(clanName);
  
  res.json({
    success: true,
    chats
  });
}));

// Create group chat
router.post('/group', catchAsync(async (req, res) => {
  const { name, description, participantIds = [] } = req.body;
  
  if (!name) {
    throw new AppError('Group name is required', 400);
  }
  
  // Add creator to participants
  const participants = [
    { user: req.user._id, role: 'owner' },
    ...participantIds.map(id => ({ user: id, role: 'member' }))
  ];
  
  const chat = new Chat({
    type: 'group',
    name,
    description,
    participants
  });
  
  await chat.save();
  await chat.populate('participants.user', 'username avatar clan');
  
  res.status(201).json({
    success: true,
    message: 'Group chat created successfully',
    chat
  });
}));

export default router;
