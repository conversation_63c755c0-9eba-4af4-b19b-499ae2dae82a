import express from 'express';
import { body, validationResult } from 'express-validator';
import MarketplaceItem from '../models/Marketplace.js';
import { catchAsync, AppError, notFoundError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get marketplace items
router.get('/', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    category, 
    minPrice, 
    maxPrice, 
    condition, 
    search 
  } = req.query;
  
  const query = {
    isActive: true,
    'availability.status': 'available'
  };
  
  if (category) query.category = category;
  if (condition) query.condition = condition;
  if (minPrice || maxPrice) {
    query['price.current'] = {};
    if (minPrice) query['price.current'].$gte = parseFloat(minPrice);
    if (maxPrice) query['price.current'].$lte = parseFloat(maxPrice);
  }
  if (search) {
    query.$or = [
      { title: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $regex: search, $options: 'i' } }
    ];
  }
  
  const items = await MarketplaceItem.find(query)
    .populate('seller', 'username avatar clan')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));
  
  const total = await MarketplaceItem.countDocuments(query);
  
  res.json({
    success: true,
    items,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Create marketplace item
router.post('/', [
  body('title').isLength({ min: 1, max: 200 }),
  body('description').isLength({ min: 1, max: 2000 }),
  body('category').isIn(['figures', 'manga', 'posters', 'accessories', 'cards', 'clothing', 'collectibles', 'other']),
  body('price.current').isFloat({ min: 0 }),
  body('condition').isIn(['new', 'like-new', 'good', 'fair', 'poor'])
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const itemData = {
    ...req.body,
    seller: req.user._id
  };
  
  const item = new MarketplaceItem(itemData);
  await item.save();
  await item.populate('seller', 'username avatar clan');
  
  res.status(201).json({
    success: true,
    message: 'Item listed successfully',
    item
  });
}));

// Get single item
router.get('/:itemId', catchAsync(async (req, res) => {
  const { itemId } = req.params;
  
  const item = await MarketplaceItem.findById(itemId)
    .populate('seller', 'username avatar clan level')
    .populate('questions.user', 'username avatar');
  
  if (!item || !item.isActive) {
    throw notFoundError('Item');
  }
  
  // Increment views
  await item.incrementViews(req.user._id);
  
  res.json({
    success: true,
    item
  });
}));

// Toggle wishlist
router.post('/:itemId/wishlist', catchAsync(async (req, res) => {
  const { itemId } = req.params;
  
  const item = await MarketplaceItem.findById(itemId);
  if (!item || !item.isActive) {
    throw notFoundError('Item');
  }
  
  const isInWishlist = item.inWishlists.some(w => 
    w.user.toString() === req.user._id.toString()
  );
  
  if (isInWishlist) {
    await item.removeFromWishlist(req.user._id);
  } else {
    await item.addToWishlist(req.user._id);
  }
  
  res.json({
    success: true,
    message: isInWishlist ? 'Removed from wishlist' : 'Added to wishlist',
    inWishlist: !isInWishlist
  });
}));

// Get user's listings
router.get('/user/:userId/listings', catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20 } = req.query;
  
  const items = await MarketplaceItem.find({
    seller: userId,
    isActive: true
  })
  .sort({ createdAt: -1 })
  .skip((page - 1) * limit)
  .limit(parseInt(limit));
  
  const total = await MarketplaceItem.countDocuments({
    seller: userId,
    isActive: true
  });
  
  res.json({
    success: true,
    items,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

export default router;
