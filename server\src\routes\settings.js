import express from 'express';
import { body, validationResult } from 'express-validator';
import User from '../models/User.js';
import { catchAsync, AppError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get user settings
router.get('/', catchAsync(async (req, res) => {
  const user = await User.findById(req.user._id).select('settings');
  
  res.json({
    success: true,
    settings: user.settings
  });
}));

// Update user settings
router.put('/', catchAsync(async (req, res) => {
  const { settings } = req.body;
  
  if (!settings) {
    throw new AppError('Settings data is required', 400);
  }
  
  const user = await User.findById(req.user._id);
  
  // Update settings
  user.settings = {
    ...user.settings,
    ...settings
  };
  
  await user.save();
  
  res.json({
    success: true,
    message: 'Settings updated successfully',
    settings: user.settings
  });
}));

// Update privacy settings
router.put('/privacy', catchAsync(async (req, res) => {
  const {
    profileVisibility,
    showOnlineStatus,
    allowDirectMessages,
    showWatchingList,
    showClanActivity,
    dataCollection
  } = req.body;
  
  const user = await User.findById(req.user._id);
  
  const privacySettings = {};
  if (profileVisibility) privacySettings['settings.profileVisibility'] = profileVisibility;
  if (showOnlineStatus !== undefined) privacySettings['settings.showOnlineStatus'] = showOnlineStatus;
  if (allowDirectMessages) privacySettings['settings.allowDirectMessages'] = allowDirectMessages;
  if (showWatchingList !== undefined) privacySettings['settings.showWatchingList'] = showWatchingList;
  if (showClanActivity !== undefined) privacySettings['settings.showClanActivity'] = showClanActivity;
  if (dataCollection !== undefined) privacySettings['settings.dataCollection'] = dataCollection;
  
  await User.findByIdAndUpdate(req.user._id, privacySettings);
  
  res.json({
    success: true,
    message: 'Privacy settings updated successfully'
  });
}));

// Update notification settings
router.put('/notifications', catchAsync(async (req, res) => {
  const {
    emailNotifications,
    pushNotifications,
    episodeAlerts,
    socialNotifications,
    eventReminders,
    marketplaceAlerts,
    clanNotifications
  } = req.body;
  
  const notificationSettings = {};
  if (emailNotifications !== undefined) notificationSettings['settings.emailNotifications'] = emailNotifications;
  if (pushNotifications !== undefined) notificationSettings['settings.pushNotifications'] = pushNotifications;
  if (episodeAlerts !== undefined) notificationSettings['settings.episodeAlerts'] = episodeAlerts;
  if (socialNotifications !== undefined) notificationSettings['settings.socialNotifications'] = socialNotifications;
  if (eventReminders !== undefined) notificationSettings['settings.eventReminders'] = eventReminders;
  if (marketplaceAlerts !== undefined) notificationSettings['settings.marketplaceAlerts'] = marketplaceAlerts;
  if (clanNotifications !== undefined) notificationSettings['settings.clanNotifications'] = clanNotifications;
  
  await User.findByIdAndUpdate(req.user._id, notificationSettings);
  
  res.json({
    success: true,
    message: 'Notification settings updated successfully'
  });
}));

// Update appearance settings
router.put('/appearance', catchAsync(async (req, res) => {
  const {
    theme,
    language,
    animationsEnabled,
    soundEffects
  } = req.body;
  
  const appearanceSettings = {};
  if (theme) appearanceSettings['settings.theme'] = theme;
  if (language) appearanceSettings['settings.language'] = language;
  if (animationsEnabled !== undefined) appearanceSettings['settings.animationsEnabled'] = animationsEnabled;
  if (soundEffects !== undefined) appearanceSettings['settings.soundEffects'] = soundEffects;
  
  await User.findByIdAndUpdate(req.user._id, appearanceSettings);
  
  res.json({
    success: true,
    message: 'Appearance settings updated successfully'
  });
}));

// Update account settings
router.put('/account', catchAsync(async (req, res) => {
  const {
    twoFactorAuth,
    loginAlerts,
    sessionTimeout
  } = req.body;
  
  const accountSettings = {};
  if (twoFactorAuth !== undefined) accountSettings['settings.twoFactorAuth'] = twoFactorAuth;
  if (loginAlerts !== undefined) accountSettings['settings.loginAlerts'] = loginAlerts;
  if (sessionTimeout !== undefined) accountSettings['settings.sessionTimeout'] = sessionTimeout;
  
  await User.findByIdAndUpdate(req.user._id, accountSettings);
  
  res.json({
    success: true,
    message: 'Account settings updated successfully'
  });
}));

// Export user data
router.get('/export-data', catchAsync(async (req, res) => {
  const user = await User.findById(req.user._id).select('-password');
  
  // In a real implementation, this would generate a comprehensive data export
  const exportData = {
    user: user.toSafeObject(),
    exportedAt: new Date().toISOString(),
    message: 'This is a basic export. Full implementation would include posts, messages, etc.'
  };
  
  res.json({
    success: true,
    data: exportData
  });
}));

export default router;
