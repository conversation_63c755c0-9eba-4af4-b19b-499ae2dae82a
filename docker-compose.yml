version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: animeverse-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: animeverse
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - animeverse-network

  # Redis for caching and sessions
  redis:
    image: redis:7.2-alpine
    container_name: animeverse-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - animeverse-network

  # API Gateway
  api-gateway:
    build:
      context: ./services/API-Gateway
      dockerfile: Dockerfile
    container_name: animeverse-api-gateway
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      PORT: 3000
      CLIENT_URL: http://localhost:5173
      JWT_SECRET: animeverse_super_secret_jwt_key_2024
      USER_SERVICE_URL: http://user-service:3001
      SOCIAL_SERVICE_URL: http://social-service:3002
      EVENT_SERVICE_URL: http://event-service:3003
      MARKETPLACE_SERVICE_URL: http://marketplace-service:3004
      CHAT_SERVICE_URL: http://chat-service:3005
      ANIME_SERVICE_URL: http://anime-service:3006
    depends_on:
      - user-service
      - social-service
      - event-service
      - marketplace-service
      - chat-service
      - anime-service
    networks:
      - animeverse-network

  # User Service
  user-service:
    build:
      context: ./User-Service
      dockerfile: Dockerfile
    container_name: animeverse-user-service
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      PORT: 3001
      MONGODB_URI: ******************************************************************
      JWT_SECRET: animeverse_super_secret_jwt_key_2024
      REDIS_URL: redis://redis:6379
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./User-Service:/app
      - /app/node_modules
    networks:
      - animeverse-network

  # Social Service
  social-service:
    build:
      context: ./services/Social-Service
      dockerfile: Dockerfile
    container_name: animeverse-social-service
    restart: unless-stopped
    ports:
      - "3002:3002"
    environment:
      NODE_ENV: development
      PORT: 3002
      MONGODB_URI: ******************************************************************
      JWT_SECRET: animeverse_super_secret_jwt_key_2024
      USER_SERVICE_URL: http://user-service:3001
    depends_on:
      - mongodb
      - user-service
    volumes:
      - ./services/Social-Service:/app
      - /app/node_modules
    networks:
      - animeverse-network

  # Event Service
  event-service:
    build:
      context: ./services/Event-Service
      dockerfile: Dockerfile
    container_name: animeverse-event-service
    restart: unless-stopped
    ports:
      - "3003:3003"
    environment:
      NODE_ENV: development
      PORT: 3003
      MONGODB_URI: ******************************************************************
      JWT_SECRET: animeverse_super_secret_jwt_key_2024
      USER_SERVICE_URL: http://user-service:3001
    depends_on:
      - mongodb
      - user-service
    volumes:
      - ./services/Event-Service:/app
      - /app/node_modules
    networks:
      - animeverse-network

  # Marketplace Service
  marketplace-service:
    build:
      context: ./services/Marketplace-Service
      dockerfile: Dockerfile
    container_name: animeverse-marketplace-service
    restart: unless-stopped
    ports:
      - "3004:3004"
    environment:
      NODE_ENV: development
      PORT: 3004
      MONGODB_URI: ******************************************************************
      JWT_SECRET: animeverse_super_secret_jwt_key_2024
      USER_SERVICE_URL: http://user-service:3001
    depends_on:
      - mongodb
      - user-service
    volumes:
      - ./services/Marketplace-Service:/app
      - /app/node_modules
    networks:
      - animeverse-network

  # Chat Service
  chat-service:
    build:
      context: ./services/Chat-Service
      dockerfile: Dockerfile
    container_name: animeverse-chat-service
    restart: unless-stopped
    ports:
      - "3005:3005"
    environment:
      NODE_ENV: development
      PORT: 3005
      MONGODB_URI: ******************************************************************
      JWT_SECRET: animeverse_super_secret_jwt_key_2024
      USER_SERVICE_URL: http://user-service:3001
      REDIS_URL: redis://redis:6379
    depends_on:
      - mongodb
      - redis
      - user-service
    volumes:
      - ./services/Chat-Service:/app
      - /app/node_modules
    networks:
      - animeverse-network

  # Anime Service
  anime-service:
    build:
      context: ./services/Anime-Service
      dockerfile: Dockerfile
    container_name: animeverse-anime-service
    restart: unless-stopped
    ports:
      - "3006:3006"
    environment:
      NODE_ENV: development
      PORT: 3006
      MONGODB_URI: ******************************************************************
      JWT_SECRET: animeverse_super_secret_jwt_key_2024
      JIKAN_API_BASE_URL: https://api.jikan.moe/v4
    depends_on:
      - mongodb
    volumes:
      - ./services/Anime-Service:/app
      - /app/node_modules
    networks:
      - animeverse-network

  # React Frontend
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: animeverse-client
    restart: unless-stopped
    ports:
      - "5173:5173"
    environment:
      VITE_API_URL: http://localhost:3000/api
      VITE_SOCKET_URL: http://localhost:3005
    depends_on:
      - api-gateway
    volumes:
      - ./client:/app
      - /app/node_modules
    networks:
      - animeverse-network

volumes:
  mongodb_data:
  redis_data:

networks:
  animeverse-network:
    driver: bridge
