import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

const userSchema = new mongoose.Schema({
  // Basic Information
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  
  // Profile Information
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
  avatar: {
    type: String,
    default: '👤'
  },
  profilePicture: {
    type: String,
    default: null
  },
  
  // Clan Information
  clan: {
    type: String,
    enum: ['Naruto', 'One Piece', 'Demon Slayer', 'Attack on Titan', 'Dragon Ball'],
    required: true
  },
  clanRank: {
    type: String,
    default: 'Genin' // Will be set based on clan
  },
  clanPoints: {
    type: Number,
    default: 0
  },
  clanJoinedAt: {
    type: Date,
    default: Date.now
  },
  
  // Anime Stats
  animeStats: {
    watching: [{
      animeId: String,
      title: String,
      currentEpisode: { type: Number, default: 1 },
      totalEpisodes: Number,
      status: { type: String, enum: ['watching', 'completed', 'dropped', 'plan-to-watch'], default: 'watching' }
    }],
    completed: [{
      animeId: String,
      title: String,
      rating: { type: Number, min: 1, max: 10 },
      completedAt: { type: Date, default: Date.now }
    }],
    favorites: [String], // anime IDs
    totalWatched: { type: Number, default: 0 },
    totalEpisodes: { type: Number, default: 0 }
  },

  // Community System (from User-Service)
  favoriteAnime: {
    type: [String],
    default: []
  },

  mainCommunity: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Community'
  },

  joinedCommunities: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Community'
  }],
  
  // Gamification
  level: {
    type: Number,
    default: 1
  },
  experience: {
    type: Number,
    default: 0
  },
  achievements: [{
    id: String,
    name: String,
    description: String,
    unlockedAt: { type: Date, default: Date.now },
    rarity: { type: String, enum: ['common', 'rare', 'epic', 'legendary'], default: 'common' }
  }],
  
  // Social
  followers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  following: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  friends: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  // Settings
  settings: {
    // Privacy Settings
    profileVisibility: { type: String, enum: ['public', 'friends', 'private'], default: 'public' },
    showOnlineStatus: { type: Boolean, default: true },
    allowDirectMessages: { type: String, enum: ['everyone', 'friends', 'clan', 'none'], default: 'everyone' },
    showWatchingList: { type: Boolean, default: true },
    showClanActivity: { type: Boolean, default: true },
    dataCollection: { type: Boolean, default: true },
    
    // Notification Settings
    emailNotifications: { type: Boolean, default: true },
    pushNotifications: { type: Boolean, default: true },
    episodeAlerts: { type: Boolean, default: true },
    socialNotifications: { type: Boolean, default: true },
    eventReminders: { type: Boolean, default: true },
    marketplaceAlerts: { type: Boolean, default: true },
    clanNotifications: { type: Boolean, default: true },
    
    // Appearance Settings
    theme: { type: String, enum: ['light', 'dark'], default: 'light' },
    language: { type: String, enum: ['en', 'fr'], default: 'en' },
    animationsEnabled: { type: Boolean, default: true },
    soundEffects: { type: Boolean, default: true },
    
    // Account Settings
    twoFactorAuth: { type: Boolean, default: false },
    loginAlerts: { type: Boolean, default: true },
    sessionTimeout: { type: Number, default: 30 } // minutes
  },
  
  // Status
  isOnline: {
    type: Boolean,
    default: false
  },
  lastSeen: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  
  // Admin
  role: {
    type: String,
    enum: ['user', 'moderator', 'admin'],
    default: 'user'
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ clan: 1 });
userSchema.index({ isOnline: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for full name or display name
userSchema.virtual('displayName').get(function() {
  return this.username;
});

// Virtual for follower count
userSchema.virtual('followerCount').get(function() {
  return this.followers.length;
});

// Virtual for following count
userSchema.virtual('followingCount').get(function() {
  return this.following.length;
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash password if it's modified
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to set clan rank
userSchema.pre('save', function(next) {
  if (this.isModified('clan')) {
    switch (this.clan) {
      case 'Naruto':
        this.clanRank = 'Genin';
        break;
      case 'One Piece':
        this.clanRank = 'Cabin Boy';
        break;
      case 'Demon Slayer':
        this.clanRank = 'Mizunoto';
        break;
      case 'Attack on Titan':
        this.clanRank = 'Cadet';
        break;
      case 'Dragon Ball':
        this.clanRank = 'Earthling';
        break;
      default:
        this.clanRank = 'Rookie';
    }
  }
  next();
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to get safe user data (without password)
userSchema.methods.toSafeObject = function() {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

// Method to update last seen
userSchema.methods.updateLastSeen = function() {
  this.lastSeen = new Date();
  return this.save();
};

// Method to add achievement
userSchema.methods.addAchievement = function(achievement) {
  const exists = this.achievements.some(a => a.id === achievement.id);
  if (!exists) {
    this.achievements.push(achievement);
    return this.save();
  }
  return Promise.resolve(this);
};

// Static method to find users by clan
userSchema.statics.findByClan = function(clan) {
  return this.find({ clan, isActive: true });
};

// Static method to get online users
userSchema.statics.getOnlineUsers = function() {
  return this.find({ isOnline: true, isActive: true });
};

export default mongoose.model('User', userSchema);
