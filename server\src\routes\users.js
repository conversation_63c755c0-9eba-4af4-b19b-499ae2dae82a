import express from 'express';
import { body, validationResult } from 'express-validator';
import User from '../models/User.js';
import Post from '../models/Post.js';
import Clan from '../models/Clan.js';
import { requireOwnershipOrAdmin, requireAdmin } from '../middleware/auth.js';
import { catchAsync, AppError, notFoundError } from '../middleware/errorHandler.js';

const router = express.Router();

// Get current user profile
router.get('/me', catchAsync(async (req, res) => {
  const user = await User.findById(req.user._id)
    .populate('followers', 'username avatar clan')
    .populate('following', 'username avatar clan');
  
  if (!user) {
    throw notFoundError('User');
  }
  
  res.json({
    success: true,
    user: user.toSafeObject()
  });
}));

// Update current user profile
router.put('/me', [
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Bio cannot exceed 500 characters'),
  
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { username, bio, email, avatar } = req.body;
  const updateData = {};
  
  // Check if username is already taken
  if (username && username !== req.user.username) {
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      throw new AppError('Username already taken', 400, 'USERNAME_EXISTS');
    }
    updateData.username = username;
  }
  
  // Check if email is already taken
  if (email && email !== req.user.email) {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      throw new AppError('Email already registered', 400, 'EMAIL_EXISTS');
    }
    updateData.email = email;
  }
  
  if (bio !== undefined) updateData.bio = bio;
  if (avatar) updateData.avatar = avatar;
  
  const user = await User.findByIdAndUpdate(
    req.user._id,
    updateData,
    { new: true, runValidators: true }
  );
  
  res.json({
    success: true,
    message: 'Profile updated successfully',
    user: user.toSafeObject()
  });
}));

// Get user by ID or username
router.get('/:identifier', catchAsync(async (req, res) => {
  const { identifier } = req.params;
  
  // Check if identifier is ObjectId or username
  const isObjectId = /^[0-9a-fA-F]{24}$/.test(identifier);
  const query = isObjectId ? { _id: identifier } : { username: identifier };
  
  const user = await User.findOne({ ...query, isActive: true })
    .populate('followers', 'username avatar clan')
    .populate('following', 'username avatar clan');
  
  if (!user) {
    throw notFoundError('User');
  }
  
  // Check privacy settings
  const isOwnProfile = req.user._id.toString() === user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isOwnProfile && !isAdmin) {
    if (user.settings.profileVisibility === 'private') {
      throw new AppError('This profile is private', 403, 'PRIVATE_PROFILE');
    }
    
    if (user.settings.profileVisibility === 'friends') {
      const isFriend = user.friends.some(friend => 
        friend.toString() === req.user._id.toString()
      );
      
      if (!isFriend) {
        throw new AppError('This profile is only visible to friends', 403, 'FRIENDS_ONLY');
      }
    }
  }
  
  // Get user's posts
  const posts = await Post.find({
    author: user._id,
    isActive: true,
    visibility: isOwnProfile || isAdmin ? { $in: ['public', 'friends', 'clan', 'private'] } : 'public'
  })
  .sort({ createdAt: -1 })
  .limit(10)
  .populate('author', 'username avatar clan');
  
  // Get user stats
  const stats = {
    postsCount: await Post.countDocuments({ author: user._id, isActive: true }),
    followersCount: user.followers.length,
    followingCount: user.following.length,
    level: user.level,
    experience: user.experience,
    clanPoints: user.clanPoints
  };
  
  res.json({
    success: true,
    user: user.toSafeObject(),
    posts,
    stats,
    isFollowing: user.followers.some(follower => 
      follower._id.toString() === req.user._id.toString()
    )
  });
}));

// Follow/Unfollow user
router.post('/:userId/follow', catchAsync(async (req, res) => {
  const { userId } = req.params;
  
  if (userId === req.user._id.toString()) {
    throw new AppError('You cannot follow yourself', 400, 'SELF_FOLLOW');
  }
  
  const userToFollow = await User.findById(userId);
  if (!userToFollow || !userToFollow.isActive) {
    throw notFoundError('User');
  }
  
  const currentUser = await User.findById(req.user._id);
  
  const isAlreadyFollowing = currentUser.following.includes(userId);
  
  if (isAlreadyFollowing) {
    // Unfollow
    currentUser.following.pull(userId);
    userToFollow.followers.pull(req.user._id);
    
    await Promise.all([currentUser.save(), userToFollow.save()]);
    
    res.json({
      success: true,
      message: `Unfollowed ${userToFollow.username}`,
      isFollowing: false
    });
  } else {
    // Follow
    currentUser.following.push(userId);
    userToFollow.followers.push(req.user._id);
    
    await Promise.all([currentUser.save(), userToFollow.save()]);
    
    // Create notification
    const Notification = (await import('../models/Notification.js')).default;
    await Notification.createNotification({
      recipient: userId,
      sender: req.user._id,
      type: 'follow',
      title: 'New Follower',
      message: `${req.user.username} started following you`,
      actionUrl: `/profile/${req.user.username}`
    });
    
    res.json({
      success: true,
      message: `Now following ${userToFollow.username}`,
      isFollowing: true
    });
  }
}));

// Get user's followers
router.get('/:userId/followers', catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20 } = req.query;
  
  const user = await User.findById(userId);
  if (!user || !user.isActive) {
    throw notFoundError('User');
  }
  
  const followers = await User.find({
    _id: { $in: user.followers },
    isActive: true
  })
  .select('username avatar clan level')
  .skip((page - 1) * limit)
  .limit(parseInt(limit))
  .sort({ username: 1 });
  
  res.json({
    success: true,
    followers,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: user.followers.length
    }
  });
}));

// Get user's following
router.get('/:userId/following', catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20 } = req.query;
  
  const user = await User.findById(userId);
  if (!user || !user.isActive) {
    throw notFoundError('User');
  }
  
  const following = await User.find({
    _id: { $in: user.following },
    isActive: true
  })
  .select('username avatar clan level')
  .skip((page - 1) * limit)
  .limit(parseInt(limit))
  .sort({ username: 1 });
  
  res.json({
    success: true,
    following,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: user.following.length
    }
  });
}));

// Search users
router.get('/search/:query', catchAsync(async (req, res) => {
  const { query } = req.params;
  const { page = 1, limit = 20, clan } = req.query;
  
  if (!query || query.length < 2) {
    throw new AppError('Search query must be at least 2 characters', 400);
  }
  
  const searchQuery = {
    $and: [
      { isActive: true },
      {
        $or: [
          { username: { $regex: query, $options: 'i' } },
          { bio: { $regex: query, $options: 'i' } }
        ]
      }
    ]
  };
  
  if (clan) {
    searchQuery.$and.push({ clan });
  }
  
  const users = await User.find(searchQuery)
    .select('username avatar clan level bio')
    .skip((page - 1) * limit)
    .limit(parseInt(limit))
    .sort({ username: 1 });
  
  const total = await User.countDocuments(searchQuery);
  
  res.json({
    success: true,
    users,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Get user's anime stats
router.get('/:userId/anime-stats', catchAsync(async (req, res) => {
  const { userId } = req.params;
  
  const user = await User.findById(userId);
  if (!user || !user.isActive) {
    throw notFoundError('User');
  }
  
  // Check privacy settings
  const isOwnProfile = req.user._id.toString() === userId;
  const isAdmin = req.user.role === 'admin';
  
  if (!isOwnProfile && !isAdmin && !user.settings.showWatchingList) {
    throw new AppError('Anime stats are private', 403, 'PRIVATE_STATS');
  }
  
  res.json({
    success: true,
    animeStats: user.animeStats
  });
}));

// Update user's anime stats
router.put('/me/anime-stats', catchAsync(async (req, res) => {
  const { watching, completed, favorites } = req.body;
  
  const updateData = {};
  if (watching) updateData['animeStats.watching'] = watching;
  if (completed) updateData['animeStats.completed'] = completed;
  if (favorites) updateData['animeStats.favorites'] = favorites;
  
  const user = await User.findByIdAndUpdate(
    req.user._id,
    updateData,
    { new: true, runValidators: true }
  );
  
  res.json({
    success: true,
    message: 'Anime stats updated successfully',
    animeStats: user.animeStats
  });
}));

// Change user clan (with point sacrifice)
router.post('/me/change-clan', [
  body('newClan')
    .isIn(['Naruto', 'One Piece', 'Demon Slayer', 'Attack on Titan', 'Dragon Ball'])
    .withMessage('Please select a valid clan')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('Validation failed', 400, 'VALIDATION_ERROR');
  }
  
  const { newClan } = req.body;
  const user = await User.findById(req.user._id);
  
  if (user.clan === newClan) {
    throw new AppError('You are already in this clan', 400, 'SAME_CLAN');
  }
  
  // Check if user has enough points (cost: 1000 points)
  const clanChangeCost = 1000;
  if (user.clanPoints < clanChangeCost) {
    throw new AppError(`Insufficient clan points. Need ${clanChangeCost} points to change clan`, 400, 'INSUFFICIENT_POINTS');
  }
  
  const oldClan = user.clan;
  
  // Update user clan and deduct points
  user.clan = newClan;
  user.clanPoints -= clanChangeCost;
  user.clanJoinedAt = new Date();
  
  await user.save();
  
  // Update clan statistics
  await Promise.all([
    Clan.findOneAndUpdate({ name: oldClan }, { $inc: { 'stats.totalMembers': -1 } }),
    Clan.findOneAndUpdate({ name: newClan }, { $inc: { 'stats.totalMembers': 1 } })
  ]);
  
  res.json({
    success: true,
    message: `Successfully changed clan from ${oldClan} to ${newClan}`,
    user: user.toSafeObject()
  });
}));

// Deactivate account
router.delete('/me', catchAsync(async (req, res) => {
  const user = await User.findByIdAndUpdate(
    req.user._id,
    { 
      isActive: false,
      isOnline: false,
      deactivatedAt: new Date()
    },
    { new: true }
  );
  
  res.json({
    success: true,
    message: 'Account deactivated successfully'
  });
}));

// Admin: Get all users
router.get('/', requireAdmin, catchAsync(async (req, res) => {
  const { page = 1, limit = 50, clan, role, isActive = true } = req.query;
  
  const query = {};
  if (clan) query.clan = clan;
  if (role) query.role = role;
  if (isActive !== undefined) query.isActive = isActive === 'true';
  
  const users = await User.find(query)
    .select('-password')
    .skip((page - 1) * limit)
    .limit(parseInt(limit))
    .sort({ createdAt: -1 });
  
  const total = await User.countDocuments(query);
  
  res.json({
    success: true,
    users,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    }
  });
}));

// Admin: Update user role
router.patch('/:userId/role', requireAdmin, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { role } = req.body;
  
  if (!['user', 'moderator', 'admin'].includes(role)) {
    throw new AppError('Invalid role', 400, 'INVALID_ROLE');
  }
  
  const user = await User.findByIdAndUpdate(
    userId,
    { role },
    { new: true, runValidators: true }
  );
  
  if (!user) {
    throw notFoundError('User');
  }
  
  res.json({
    success: true,
    message: `User role updated to ${role}`,
    user: user.toSafeObject()
  });
}));

export default router;
